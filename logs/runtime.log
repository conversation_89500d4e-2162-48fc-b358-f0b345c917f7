[09-12 08:50:05.691] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 08:50:05.691] [debug] =====算法库初始化开始=====
[09-12 08:50:05.691] [info] Initializing unified resource manager...
[09-12 08:50:05.711] [info] Initializing GPU resource manager...
[09-12 08:50:06.099] [debug] CUDA stream created: 0x5555560687f0
[09-12 08:50:06.099] [info] GPU resource manager initialized successfully
[09-12 08:50:06.099] [info] Initializing memory pool manager...
[09-12 08:50:06.099] [info] CUDA memory pool created with max size: 512MB
[09-12 08:50:06.100] [info] Host memory pool created with max size: 1024MB
[09-12 08:50:06.100] [info] Memory pool manager initialized successfully
[09-12 08:50:06.100] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-12 08:50:06.100] [info] Initializing FFT resource pools for 5 segment lengths
[09-12 08:50:06.100] [debug] Initializing resource pool for length: 512
[09-12 08:50:06.100] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a00000
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a01200
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a02400
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a03600
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a04800
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a05a00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a06c00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a07e00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a09000
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a0a200
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 08:50:06.101] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-12 08:50:06.101] [debug] Initializing resource pool for length: 256
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0b400
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0be00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0c800
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0d200
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0dc00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0e600
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0f000
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0fa00
[09-12 08:50:06.101] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.101] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10e00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 08:50:06.102] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-12 08:50:06.102] [debug] Initializing resource pool for length: 128
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11e00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12a00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13000
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13600
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13c00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14200
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14e00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 08:50:06.102] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-12 08:50:06.102] [debug] Initializing resource pool for length: 64
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15c00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16000
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16c00
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17000
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17400
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17800
[09-12 08:50:06.102] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 08:50:06.102] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-12 08:50:06.102] [info] All FFT resource pools initialized successfully
[09-12 08:50:06.102] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-12 08:50:06.102] [info] Unified resource manager initialized successfully
[09-12 08:50:06.102] [info] 重新配置日志系统 - 路径: logs/runtime.log, 等级: debug
[09-12 08:50:06.103] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 08:50:06.103] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-12 08:50:06.127] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-12 08:50:06.127] [info] Engine info: 2 bindings
[09-12 08:50:06.127] [info]   Binding 0: input [1x1x1024x1024x2]
[09-12 08:50:06.127] [info]   Binding 1: output [1x1x512x1024]
[09-12 08:50:06.127] [info] 模型输入维度: [1x1024x1024x2]
[09-12 08:50:06.128] [debug] CUDA memory allocated: 8388608 bytes at 0x7fff6a600000
[09-12 08:50:06.128] [debug] CUDA memory allocated: 2097152 bytes at 0x7fff6ae00000
[09-12 08:50:06.128] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-12 08:50:06.131] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-12 08:50:06.165] [debug] - 显存 - 算法初始化完成: 使用 1021.9 MB
[09-12 08:50:06.165] [debug] - 内存 - 算法初始化完成: 使用 259.5 MB
[09-12 08:50:06.165] [debug] =====算法库初始化完成=====
[09-12 08:50:06.844] [debug] =====开始目标检测=====
[09-12 08:50:06.844] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.584] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 08:50:07.584] [debug] =====开始目标跟踪======
[09-12 08:50:07.584] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.584] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.584] [debug] 跟踪结果数量: 0
[09-12 08:50:07.584] [debug] 开始释放检测结果数组
[09-12 08:50:07.584] [debug] 结束释放检测结果数组
[09-12 08:50:07.584] [debug] 开始释放列数据段
[09-12 08:50:07.584] [debug] 结束释放列数据段
[09-12 08:50:07.584] [debug] 已释放内部检测数据内存
[09-12 08:50:07.586] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.586] [debug] - 内存 - 目标跟踪完成: 使用 1037.7 MB
[09-12 08:50:07.586] [debug] =====开始目标检测=====
[09-12 08:50:07.586] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.594] [warning] 无效帧:2458 模型没有检测到目标，跳过处理
[09-12 08:50:07.595] [debug] =====开始目标跟踪======
[09-12 08:50:07.595] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.595] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.595] [debug] 跟踪结果数量: 0
[09-12 08:50:07.595] [debug] 开始释放检测结果数组
[09-12 08:50:07.595] [debug] 结束释放检测结果数组
[09-12 08:50:07.595] [debug] 开始释放列数据段
[09-12 08:50:07.595] [debug] 结束释放列数据段
[09-12 08:50:07.595] [debug] 已释放内部检测数据内存
[09-12 08:50:07.595] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.595] [debug] - 内存 - 目标跟踪完成: 使用 999.9 MB
[09-12 08:50:07.597] [debug] =====开始目标检测=====
[09-12 08:50:07.597] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.606] [warning] 无效帧:2459 模型没有检测到目标，跳过处理
[09-12 08:50:07.606] [debug] =====开始目标跟踪======
[09-12 08:50:07.606] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.607] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.607] [debug] 跟踪结果数量: 0
[09-12 08:50:07.607] [debug] 开始释放检测结果数组
[09-12 08:50:07.607] [debug] 结束释放检测结果数组
[09-12 08:50:07.607] [debug] 开始释放列数据段
[09-12 08:50:07.607] [debug] 结束释放列数据段
[09-12 08:50:07.607] [debug] 已释放内部检测数据内存
[09-12 08:50:07.607] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.607] [debug] - 内存 - 目标跟踪完成: 使用 923.7 MB
[09-12 08:50:07.610] [debug] =====开始目标检测=====
[09-12 08:50:07.610] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.618] [warning] 无效帧:2460 模型没有检测到目标，跳过处理
[09-12 08:50:07.618] [debug] =====开始目标跟踪======
[09-12 08:50:07.618] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.618] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.618] [debug] 跟踪结果数量: 0
[09-12 08:50:07.618] [debug] 开始释放检测结果数组
[09-12 08:50:07.618] [debug] 结束释放检测结果数组
[09-12 08:50:07.618] [debug] 开始释放列数据段
[09-12 08:50:07.618] [debug] 结束释放列数据段
[09-12 08:50:07.618] [debug] 已释放内部检测数据内存
[09-12 08:50:07.618] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.618] [debug] - 内存 - 目标跟踪完成: 使用 835.9 MB
[09-12 08:50:07.621] [debug] =====开始目标检测=====
[09-12 08:50:07.621] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.630] [warning] 无效帧:2461 模型没有检测到目标，跳过处理
[09-12 08:50:07.631] [debug] =====开始目标跟踪======
[09-12 08:50:07.631] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.631] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.631] [debug] 跟踪结果数量: 0
[09-12 08:50:07.631] [debug] 开始释放检测结果数组
[09-12 08:50:07.631] [debug] 结束释放检测结果数组
[09-12 08:50:07.631] [debug] 开始释放列数据段
[09-12 08:50:07.631] [debug] 结束释放列数据段
[09-12 08:50:07.631] [debug] 已释放内部检测数据内存
[09-12 08:50:07.631] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.631] [debug] - 内存 - 目标跟踪完成: 使用 715.6 MB
[09-12 08:50:07.633] [debug] =====开始目标检测=====
[09-12 08:50:07.633] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.642] [debug] 检测到 1 个目标中心点
[09-12 08:50:07.642] [debug] 开始提取列数据段，共 1 个中心点
[09-12 08:50:07.642] [debug] 检测点[0]: 帧号=2462, 列=1792, 行=565, 段长度=256
[09-12 08:50:07.642] [debug] 目标检测耗时(ms): 预处理:3 推理:2 后处理:2 提取:0 总:7 (FPS:142.86)
[09-12 08:50:07.642] [debug] =====开始目标跟踪======
[09-12 08:50:07.642] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 08:50:07.642] [debug] 处理检测点[0]: 列=1792, 行=565
[09-12 08:50:07.642] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 08:50:07.642] [debug] Released resources in pool (length 256), slot 0
[09-12 08:50:07.642] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 08:50:07.642] [debug] Released resources in pool (length 256), slot 0
[09-12 08:50:07.643] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 08:50:07.643] [info] Frame: 2462, Velo:   9.70, Range: 971.00, Amaz:  23.25, Elev:   7.20, Altitude: 121.70, X_cor: 1792, Y_cor: 565
[09-12 08:50:07.647] [debug] 跟踪结果数量: 0
[09-12 08:50:07.647] [debug] 开始释放检测结果数组
[09-12 08:50:07.647] [debug] 结束释放检测结果数组
[09-12 08:50:07.647] [debug] 开始释放列数据段
[09-12 08:50:07.647] [debug] 结束释放列数据段
[09-12 08:50:07.647] [debug] 已释放内部检测数据内存
[09-12 08:50:07.647] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:07.647] [debug] - 内存 - 目标跟踪完成: 使用 557.8 MB
[09-12 08:50:07.647] [debug] =====开始目标检测=====
[09-12 08:50:07.647] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.659] [debug] 检测到 1 个目标中心点
[09-12 08:50:07.659] [debug] 开始提取列数据段，共 1 个中心点
[09-12 08:50:07.659] [debug] 检测点[0]: 帧号=2463, 列=1791, 行=564, 段长度=256
[09-12 08:50:07.659] [debug] 目标检测耗时(ms): 预处理:7 推理:2 后处理:1 提取:0 总:10 (FPS:100.00)
[09-12 08:50:07.659] [debug] =====开始目标跟踪======
[09-12 08:50:07.659] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 08:50:07.659] [debug] 处理检测点[0]: 列=1791, 行=564
[09-12 08:50:07.659] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 08:50:07.659] [debug] Released resources in pool (length 256), slot 0
[09-12 08:50:07.659] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 08:50:07.659] [debug] Released resources in pool (length 256), slot 0
[09-12 08:50:07.659] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 08:50:07.659] [info] Frame: 2463, Velo:   9.52, Range: 968.00, Amaz:  26.33, Elev:   5.80, Altitude:  97.82, X_cor: 1791, Y_cor: 564
[09-12 08:50:07.668] [debug] 跟踪结果数量: 1
[09-12 08:50:07.668] [info] Track[ 0] ID:  0 | Pos: ( 863.15,  427.10,   97.85) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.00 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:07.668] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:07.668] [debug] 开始释放检测结果数组
[09-12 08:50:07.668] [debug] 结束释放检测结果数组
[09-12 08:50:07.668] [debug] 开始释放列数据段
[09-12 08:50:07.668] [debug] 结束释放列数据段
[09-12 08:50:07.668] [debug] 已释放内部检测数据内存
[09-12 08:50:07.668] [debug] - 显存 - 目标跟踪完成: 使用 1021.8 MB
[09-12 08:50:07.668] [debug] - 内存 - 目标跟踪完成: 使用 461.8 MB
[09-12 08:50:07.980] [debug] =====开始目标检测=====
[09-12 08:50:07.980] [debug] 提取到 1024 个有效帧头
[09-12 08:50:07.990] [warning] 无效帧:2464 模型没有检测到目标，跳过处理
[09-12 08:50:07.990] [debug] =====开始目标跟踪======
[09-12 08:50:07.990] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:07.990] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:07.990] [debug] 跟踪结果数量: 0
[09-12 08:50:07.990] [info] Track[ 0] ID:  0 | Pos: ( 863.58,  427.31,   97.89) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.48 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:07.990] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:07.990] [debug] 开始释放检测结果数组
[09-12 08:50:07.990] [debug] 结束释放检测结果数组
[09-12 08:50:07.990] [debug] 开始释放列数据段
[09-12 08:50:07.990] [debug] 结束释放列数据段
[09-12 08:50:07.990] [debug] 已释放内部检测数据内存
[09-12 08:50:07.990] [debug] - 显存 - 目标跟踪完成: 使用 1021.8 MB
[09-12 08:50:07.990] [debug] - 内存 - 目标跟踪完成: 使用 469.9 MB
[09-12 08:50:08.382] [debug] =====开始目标检测=====
[09-12 08:50:08.382] [debug] 提取到 1024 个有效帧头
[09-12 08:50:08.382] [info] 方位角变化，角度过滤，跳过帧: 2465，角度: 31.83，目标数量: 0, 列数据段数量: 0 
[09-12 08:50:08.382] [debug] =====开始目标跟踪======
[09-12 08:50:08.382] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:08.382] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:08.382] [debug] 跟踪结果数量: 0
[09-12 08:50:08.382] [info] Track[ 0] ID:  0 | Pos: ( 864.00,  427.52,   97.94) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.95 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:08.382] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:08.382] [debug] 开始释放检测结果数组
[09-12 08:50:08.382] [debug] 结束释放检测结果数组
[09-12 08:50:08.382] [debug] 开始释放列数据段
[09-12 08:50:08.382] [debug] 结束释放列数据段
[09-12 08:50:08.382] [debug] 已释放内部检测数据内存
[09-12 08:50:08.382] [debug] - 显存 - 目标跟踪完成: 使用 1020.4 MB
[09-12 08:50:08.382] [debug] - 内存 - 目标跟踪完成: 使用 434.5 MB
[09-12 08:50:08.897] [debug] =====开始目标检测=====
[09-12 08:50:08.897] [debug] 提取到 1024 个有效帧头
[09-12 08:50:08.897] [info] 方位角变化，角度过滤，跳过帧: 2466，角度: 34.91，目标数量: 0, 列数据段数量: 0 
[09-12 08:50:08.897] [debug] =====开始目标跟踪======
[09-12 08:50:08.897] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:08.897] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:08.897] [debug] 跟踪结果数量: 0
[09-12 08:50:08.897] [info] Track[ 0] ID:  0 | Pos: ( 864.43,  427.73,   97.99) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.43 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:08.897] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:08.897] [debug] 开始释放检测结果数组
[09-12 08:50:08.897] [debug] 结束释放检测结果数组
[09-12 08:50:08.897] [debug] 开始释放列数据段
[09-12 08:50:08.897] [debug] 结束释放列数据段
[09-12 08:50:08.897] [debug] 已释放内部检测数据内存
[09-12 08:50:08.897] [debug] - 显存 - 目标跟踪完成: 使用 1020.1 MB
[09-12 08:50:08.897] [debug] - 内存 - 目标跟踪完成: 使用 428.4 MB
[09-12 08:50:09.517] [debug] =====开始目标检测=====
[09-12 08:50:09.517] [debug] 提取到 1024 个有效帧头
[09-12 08:50:09.517] [info] 方位角变化，角度过滤，跳过帧: 2467，角度: 38.01，目标数量: 0, 列数据段数量: 0 
[09-12 08:50:09.517] [debug] =====开始目标跟踪======
[09-12 08:50:09.518] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:09.518] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:09.518] [debug] 跟踪结果数量: 0
[09-12 08:50:09.518] [info] Track[ 0] ID:  0 | Pos: ( 864.85,  427.94,   98.04) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.91 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 08:50:09.518] [debug] 跟踪完成，输出 1 个轨迹
[09-12 08:50:09.518] [debug] 开始释放检测结果数组
[09-12 08:50:09.518] [debug] 结束释放检测结果数组
[09-12 08:50:09.518] [debug] 开始释放列数据段
[09-12 08:50:09.518] [debug] 结束释放列数据段
[09-12 08:50:09.518] [debug] 已释放内部检测数据内存
[09-12 08:50:09.518] [debug] - 显存 - 目标跟踪完成: 使用 1014.4 MB
[09-12 08:50:09.518] [debug] - 内存 - 目标跟踪完成: 使用 427.7 MB
[09-12 08:50:09.966] [debug] =====开始目标检测=====
[09-12 08:50:09.966] [debug] 提取到 1024 个有效帧头
[09-12 08:50:09.966] [info] 方位角变化，角度过滤，跳过帧: 2468，角度: 41.08，目标数量: 0, 列数据段数量: 0 
[09-12 08:50:09.966] [debug] =====开始目标跟踪======
[09-12 08:50:09.966] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 08:50:09.966] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 08:50:09.966] [debug] 跟踪结果数量: 0
[09-12 08:50:09.966] [info] Track[ 0] ID:  0 | Pos: ( 865.28,  428.15,   98.09) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.38 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:35.665] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 09:30:35.665] [debug] =====算法库初始化开始=====
[09-12 09:30:35.665] [info] Initializing unified resource manager...
[09-12 09:30:35.666] [info] Initializing GPU resource manager...
[09-12 09:30:35.739] [debug] CUDA stream created: 0x5555560687f0
[09-12 09:30:35.739] [info] GPU resource manager initialized successfully
[09-12 09:30:35.739] [info] Initializing memory pool manager...
[09-12 09:30:35.739] [info] CUDA memory pool created with max size: 512MB
[09-12 09:30:35.739] [info] Host memory pool created with max size: 1024MB
[09-12 09:30:35.739] [info] Memory pool manager initialized successfully
[09-12 09:30:35.739] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-12 09:30:35.739] [info] Initializing FFT resource pools for 5 segment lengths
[09-12 09:30:35.739] [debug] Initializing resource pool for length: 512
[09-12 09:30:35.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a00000
[09-12 09:30:35.740] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.740] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a01200
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a02400
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a03600
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a04800
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a05a00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a06c00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a07e00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a09000
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a0a200
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:30:35.741] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-12 09:30:35.741] [debug] Initializing resource pool for length: 256
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0b400
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0be00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0c800
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0d200
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0dc00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0e600
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0f000
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0fa00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10400
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10e00
[09-12 09:30:35.741] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:30:35.741] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-12 09:30:35.741] [debug] Initializing resource pool for length: 128
[09-12 09:30:35.741] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11e00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12400
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12a00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13000
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13600
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13c00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14200
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14e00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:30:35.742] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-12 09:30:35.742] [debug] Initializing resource pool for length: 64
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15400
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15c00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16000
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16400
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16c00
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17000
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17400
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17800
[09-12 09:30:35.742] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:30:35.742] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-12 09:30:35.742] [info] All FFT resource pools initialized successfully
[09-12 09:30:35.742] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-12 09:30:35.742] [info] Unified resource manager initialized successfully
[09-12 09:30:35.742] [info] 重新配置日志系统 - 路径: logs/runtime.log, 等级: debug
[09-12 09:30:35.743] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 09:30:35.743] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-12 09:30:35.758] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-12 09:30:35.758] [info] Engine info: 2 bindings
[09-12 09:30:35.758] [info]   Binding 0: input [1x1x1024x1024x2]
[09-12 09:30:35.758] [info]   Binding 1: output [1x1x512x1024]
[09-12 09:30:35.758] [info] 模型输入维度: [1x1024x1024x2]
[09-12 09:30:35.758] [debug] CUDA memory allocated: 8388608 bytes at 0x7fff6a600000
[09-12 09:30:35.758] [debug] CUDA memory allocated: 2097152 bytes at 0x7fff6ae00000
[09-12 09:30:35.758] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-12 09:30:35.761] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-12 09:30:35.762] [debug] - 显存 - 算法初始化完成: 使用 953.9 MB
[09-12 09:30:35.762] [debug] - 内存 - 算法初始化完成: 使用 259.1 MB
[09-12 09:30:35.762] [debug] =====算法库初始化完成=====
[09-12 09:30:35.880] [debug] =====开始目标检测=====
[09-12 09:30:35.881] [debug] 提取到 1024 个有效帧头
[09-12 09:30:35.960] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 09:30:35.960] [debug] =====开始目标跟踪======
[09-12 09:30:35.960] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:35.960] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:35.960] [debug] 跟踪结果数量: 0
[09-12 09:30:35.960] [debug] 开始释放检测结果数组
[09-12 09:30:35.960] [debug] 结束释放检测结果数组
[09-12 09:30:35.960] [debug] 开始释放列数据段
[09-12 09:30:35.960] [debug] 结束释放列数据段
[09-12 09:30:35.960] [debug] 已释放内部检测数据内存
[09-12 09:30:35.962] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:35.962] [debug] - 内存 - 目标跟踪完成: 使用 482.6 MB
[09-12 09:30:36.000] [debug] =====开始目标检测=====
[09-12 09:30:36.000] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.012] [warning] 无效帧:2458 模型没有检测到目标，跳过处理
[09-12 09:30:36.012] [debug] =====开始目标跟踪======
[09-12 09:30:36.012] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.012] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.012] [debug] 跟踪结果数量: 0
[09-12 09:30:36.012] [debug] 开始释放检测结果数组
[09-12 09:30:36.012] [debug] 结束释放检测结果数组
[09-12 09:30:36.012] [debug] 开始释放列数据段
[09-12 09:30:36.012] [debug] 结束释放列数据段
[09-12 09:30:36.012] [debug] 已释放内部检测数据内存
[09-12 09:30:36.012] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:36.013] [debug] - 内存 - 目标跟踪完成: 使用 454.8 MB
[09-12 09:30:36.116] [debug] =====开始目标检测=====
[09-12 09:30:36.116] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.124] [warning] 无效帧:2459 模型没有检测到目标，跳过处理
[09-12 09:30:36.124] [debug] =====开始目标跟踪======
[09-12 09:30:36.124] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.124] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.124] [debug] 跟踪结果数量: 0
[09-12 09:30:36.124] [debug] 开始释放检测结果数组
[09-12 09:30:36.124] [debug] 结束释放检测结果数组
[09-12 09:30:36.124] [debug] 开始释放列数据段
[09-12 09:30:36.124] [debug] 结束释放列数据段
[09-12 09:30:36.124] [debug] 已释放内部检测数据内存
[09-12 09:30:36.124] [debug] - 显存 - 目标跟踪完成: 使用 955.2 MB
[09-12 09:30:36.124] [debug] - 内存 - 目标跟踪完成: 使用 477.9 MB
[09-12 09:30:36.236] [debug] =====开始目标检测=====
[09-12 09:30:36.236] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.247] [warning] 无效帧:2460 模型没有检测到目标，跳过处理
[09-12 09:30:36.247] [debug] =====开始目标跟踪======
[09-12 09:30:36.247] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.247] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.247] [debug] 跟踪结果数量: 0
[09-12 09:30:36.247] [debug] 开始释放检测结果数组
[09-12 09:30:36.247] [debug] 结束释放检测结果数组
[09-12 09:30:36.247] [debug] 开始释放列数据段
[09-12 09:30:36.247] [debug] 结束释放列数据段
[09-12 09:30:36.247] [debug] 已释放内部检测数据内存
[09-12 09:30:36.247] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:36.247] [debug] - 内存 - 目标跟踪完成: 使用 455.0 MB
[09-12 09:30:36.355] [debug] =====开始目标检测=====
[09-12 09:30:36.355] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.364] [warning] 无效帧:2461 模型没有检测到目标，跳过处理
[09-12 09:30:36.364] [debug] =====开始目标跟踪======
[09-12 09:30:36.364] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.364] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.364] [debug] 跟踪结果数量: 0
[09-12 09:30:36.364] [debug] 开始释放检测结果数组
[09-12 09:30:36.364] [debug] 结束释放检测结果数组
[09-12 09:30:36.364] [debug] 开始释放列数据段
[09-12 09:30:36.364] [debug] 结束释放列数据段
[09-12 09:30:36.364] [debug] 已释放内部检测数据内存
[09-12 09:30:36.364] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:36.364] [debug] - 内存 - 目标跟踪完成: 使用 452.0 MB
[09-12 09:30:36.476] [debug] =====开始目标检测=====
[09-12 09:30:36.476] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.486] [debug] 检测到 1 个目标中心点
[09-12 09:30:36.486] [debug] 开始提取列数据段，共 1 个中心点
[09-12 09:30:36.486] [debug] 检测点[0]: 帧号=2462, 列=1792, 行=565, 段长度=256
[09-12 09:30:36.486] [debug] 目标检测耗时(ms): 预处理:4 推理:2 后处理:3 提取:0 总:9 (FPS:111.11)
[09-12 09:30:36.488] [debug] =====开始目标跟踪======
[09-12 09:30:36.488] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 09:30:36.488] [debug] 处理检测点[0]: 列=1792, 行=565
[09-12 09:30:36.488] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:30:36.488] [debug] Released resources in pool (length 256), slot 0
[09-12 09:30:36.488] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:30:36.488] [debug] Released resources in pool (length 256), slot 0
[09-12 09:30:36.488] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 09:30:36.488] [info] Frame: 2462, Velo:   9.70, Range: 971.00, Amaz:  23.25, Elev:   7.20, Altitude: 121.70, X_cor: 1792, Y_cor: 565
[09-12 09:30:36.492] [debug] 跟踪结果数量: 0
[09-12 09:30:36.492] [debug] 开始释放检测结果数组
[09-12 09:30:36.492] [debug] 结束释放检测结果数组
[09-12 09:30:36.492] [debug] 开始释放列数据段
[09-12 09:30:36.492] [debug] 结束释放列数据段
[09-12 09:30:36.492] [debug] 已释放内部检测数据内存
[09-12 09:30:36.492] [debug] - 显存 - 目标跟踪完成: 使用 953.9 MB
[09-12 09:30:36.492] [debug] - 内存 - 目标跟踪完成: 使用 397.4 MB
[09-12 09:30:36.604] [debug] =====开始目标检测=====
[09-12 09:30:36.604] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.614] [debug] 检测到 1 个目标中心点
[09-12 09:30:36.614] [debug] 开始提取列数据段，共 1 个中心点
[09-12 09:30:36.614] [debug] 检测点[0]: 帧号=2463, 列=1791, 行=564, 段长度=256
[09-12 09:30:36.614] [debug] 目标检测耗时(ms): 预处理:3 推理:2 后处理:4 提取:0 总:9 (FPS:111.11)
[09-12 09:30:36.615] [debug] =====开始目标跟踪======
[09-12 09:30:36.615] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 09:30:36.615] [debug] 处理检测点[0]: 列=1791, 行=564
[09-12 09:30:36.615] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:30:36.615] [debug] Released resources in pool (length 256), slot 0
[09-12 09:30:36.615] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:30:36.615] [debug] Released resources in pool (length 256), slot 0
[09-12 09:30:36.615] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 09:30:36.615] [info] Frame: 2463, Velo:   9.52, Range: 968.00, Amaz:  26.33, Elev:   5.80, Altitude:  97.82, X_cor: 1791, Y_cor: 564
[09-12 09:30:36.618] [debug] 跟踪结果数量: 1
[09-12 09:30:36.618] [info] Track[ 0] ID:  0 | Pos: ( 863.15,  427.10,   97.85) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.00 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:36.618] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:36.618] [debug] 开始释放检测结果数组
[09-12 09:30:36.618] [debug] 结束释放检测结果数组
[09-12 09:30:36.618] [debug] 开始释放列数据段
[09-12 09:30:36.618] [debug] 结束释放列数据段
[09-12 09:30:36.618] [debug] 已释放内部检测数据内存
[09-12 09:30:36.618] [debug] - 显存 - 目标跟踪完成: 使用 953.0 MB
[09-12 09:30:36.618] [debug] - 内存 - 目标跟踪完成: 使用 449.2 MB
[09-12 09:30:36.727] [debug] =====开始目标检测=====
[09-12 09:30:36.727] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.737] [warning] 无效帧:2464 模型没有检测到目标，跳过处理
[09-12 09:30:36.738] [debug] =====开始目标跟踪======
[09-12 09:30:36.738] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.738] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.738] [debug] 跟踪结果数量: 0
[09-12 09:30:36.738] [info] Track[ 0] ID:  0 | Pos: ( 863.58,  427.31,   97.89) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.48 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:36.738] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:36.738] [debug] 开始释放检测结果数组
[09-12 09:30:36.738] [debug] 结束释放检测结果数组
[09-12 09:30:36.738] [debug] 开始释放列数据段
[09-12 09:30:36.738] [debug] 结束释放列数据段
[09-12 09:30:36.738] [debug] 已释放内部检测数据内存
[09-12 09:30:36.739] [debug] - 显存 - 目标跟踪完成: 使用 952.9 MB
[09-12 09:30:36.739] [debug] - 内存 - 目标跟踪完成: 使用 455.5 MB
[09-12 09:30:36.842] [debug] =====开始目标检测=====
[09-12 09:30:36.842] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.842] [info] 方位角变化，角度过滤，跳过帧: 2465，角度: 31.83，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:36.842] [debug] =====开始目标跟踪======
[09-12 09:30:36.842] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.842] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.842] [debug] 跟踪结果数量: 0
[09-12 09:30:36.842] [info] Track[ 0] ID:  0 | Pos: ( 864.00,  427.52,   97.94) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.95 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:36.842] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:36.842] [debug] 开始释放检测结果数组
[09-12 09:30:36.842] [debug] 结束释放检测结果数组
[09-12 09:30:36.842] [debug] 开始释放列数据段
[09-12 09:30:36.842] [debug] 结束释放列数据段
[09-12 09:30:36.842] [debug] 已释放内部检测数据内存
[09-12 09:30:36.843] [debug] - 显存 - 目标跟踪完成: 使用 952.9 MB
[09-12 09:30:36.843] [debug] - 内存 - 目标跟踪完成: 使用 426.3 MB
[09-12 09:30:36.951] [debug] =====开始目标检测=====
[09-12 09:30:36.951] [debug] 提取到 1024 个有效帧头
[09-12 09:30:36.951] [info] 方位角变化，角度过滤，跳过帧: 2466，角度: 34.91，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:36.951] [debug] =====开始目标跟踪======
[09-12 09:30:36.951] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:36.951] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:36.951] [debug] 跟踪结果数量: 0
[09-12 09:30:36.951] [info] Track[ 0] ID:  0 | Pos: ( 864.43,  427.73,   97.99) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.43 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:36.951] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:36.951] [debug] 开始释放检测结果数组
[09-12 09:30:36.951] [debug] 结束释放检测结果数组
[09-12 09:30:36.951] [debug] 开始释放列数据段
[09-12 09:30:36.951] [debug] 结束释放列数据段
[09-12 09:30:36.951] [debug] 已释放内部检测数据内存
[09-12 09:30:36.951] [debug] - 显存 - 目标跟踪完成: 使用 952.9 MB
[09-12 09:30:36.951] [debug] - 内存 - 目标跟踪完成: 使用 450.8 MB
[09-12 09:30:37.058] [debug] =====开始目标检测=====
[09-12 09:30:37.058] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.058] [info] 方位角变化，角度过滤，跳过帧: 2467，角度: 38.01，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.058] [debug] =====开始目标跟踪======
[09-12 09:30:37.058] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.058] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.058] [debug] 跟踪结果数量: 0
[09-12 09:30:37.058] [info] Track[ 0] ID:  0 | Pos: ( 864.85,  427.94,   98.04) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.91 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.058] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.058] [debug] 开始释放检测结果数组
[09-12 09:30:37.058] [debug] 结束释放检测结果数组
[09-12 09:30:37.058] [debug] 开始释放列数据段
[09-12 09:30:37.058] [debug] 结束释放列数据段
[09-12 09:30:37.058] [debug] 已释放内部检测数据内存
[09-12 09:30:37.058] [debug] - 显存 - 目标跟踪完成: 使用 953.4 MB
[09-12 09:30:37.058] [debug] - 内存 - 目标跟踪完成: 使用 425.1 MB
[09-12 09:30:37.169] [debug] =====开始目标检测=====
[09-12 09:30:37.169] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.169] [info] 方位角变化，角度过滤，跳过帧: 2468，角度: 41.08，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.169] [debug] =====开始目标跟踪======
[09-12 09:30:37.169] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.169] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.169] [debug] 跟踪结果数量: 0
[09-12 09:30:37.169] [info] Track[ 0] ID:  0 | Pos: ( 865.28,  428.15,   98.09) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.38 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.169] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.169] [debug] 开始释放检测结果数组
[09-12 09:30:37.169] [debug] 结束释放检测结果数组
[09-12 09:30:37.169] [debug] 开始释放列数据段
[09-12 09:30:37.169] [debug] 结束释放列数据段
[09-12 09:30:37.169] [debug] 已释放内部检测数据内存
[09-12 09:30:37.169] [debug] - 显存 - 目标跟踪完成: 使用 953.4 MB
[09-12 09:30:37.169] [debug] - 内存 - 目标跟踪完成: 使用 434.6 MB
[09-12 09:30:37.283] [debug] =====开始目标检测=====
[09-12 09:30:37.283] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.283] [info] 方位角变化，角度过滤，跳过帧: 2469，角度: 44.15，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.283] [debug] =====开始目标跟踪======
[09-12 09:30:37.283] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.283] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.284] [debug] 跟踪结果数量: 0
[09-12 09:30:37.284] [info] Track[ 0] ID:  0 | Pos: ( 865.70,  428.36,   98.13) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.86 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.284] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.284] [debug] 开始释放检测结果数组
[09-12 09:30:37.284] [debug] 结束释放检测结果数组
[09-12 09:30:37.284] [debug] 开始释放列数据段
[09-12 09:30:37.284] [debug] 结束释放列数据段
[09-12 09:30:37.284] [debug] 已释放内部检测数据内存
[09-12 09:30:37.284] [debug] - 显存 - 目标跟踪完成: 使用 954.4 MB
[09-12 09:30:37.284] [debug] - 内存 - 目标跟踪完成: 使用 425.5 MB
[09-12 09:30:37.568] [debug] =====开始目标检测=====
[09-12 09:30:37.568] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.568] [info] 方位角变化，角度过滤，跳过帧: 2470，角度: 47.25，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.568] [debug] =====开始目标跟踪======
[09-12 09:30:37.568] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.568] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.568] [debug] 跟踪结果数量: 0
[09-12 09:30:37.568] [info] Track[ 0] ID:  0 | Pos: ( 866.13,  428.57,   98.18) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.33 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.568] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.568] [debug] 开始释放检测结果数组
[09-12 09:30:37.568] [debug] 结束释放检测结果数组
[09-12 09:30:37.568] [debug] 开始释放列数据段
[09-12 09:30:37.568] [debug] 结束释放列数据段
[09-12 09:30:37.568] [debug] 已释放内部检测数据内存
[09-12 09:30:37.568] [debug] - 显存 - 目标跟踪完成: 使用 954.6 MB
[09-12 09:30:37.568] [debug] - 内存 - 目标跟踪完成: 使用 434.2 MB
[09-12 09:30:37.767] [debug] =====开始目标检测=====
[09-12 09:30:37.767] [debug] 提取到 1024 个有效帧头
[09-12 09:30:37.767] [info] 方位角变化，角度过滤，跳过帧: 2471，角度: 50.31，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:37.767] [debug] =====开始目标跟踪======
[09-12 09:30:37.767] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:37.767] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:37.767] [debug] 跟踪结果数量: 0
[09-12 09:30:37.767] [info] Track[ 0] ID:  0 | Pos: ( 866.55,  428.78,   98.23) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.81 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:37.767] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:37.767] [debug] 开始释放检测结果数组
[09-12 09:30:37.767] [debug] 结束释放检测结果数组
[09-12 09:30:37.767] [debug] 开始释放列数据段
[09-12 09:30:37.767] [debug] 结束释放列数据段
[09-12 09:30:37.767] [debug] 已释放内部检测数据内存
[09-12 09:30:37.767] [debug] - 显存 - 目标跟踪完成: 使用 954.4 MB
[09-12 09:30:37.767] [debug] - 内存 - 目标跟踪完成: 使用 434.4 MB
[09-12 09:30:38.076] [debug] =====开始目标检测=====
[09-12 09:30:38.076] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.076] [info] 方位角变化，角度过滤，跳过帧: 2472，角度: 53.39，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:38.076] [debug] =====开始目标跟踪======
[09-12 09:30:38.076] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.076] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.076] [debug] 跟踪结果数量: 0
[09-12 09:30:38.076] [info] Track[ 0] ID:  0 | Pos: ( 866.97,  428.99,   98.28) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.29 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.076] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.076] [debug] 开始释放检测结果数组
[09-12 09:30:38.076] [debug] 结束释放检测结果数组
[09-12 09:30:38.076] [debug] 开始释放列数据段
[09-12 09:30:38.076] [debug] 结束释放列数据段
[09-12 09:30:38.076] [debug] 已释放内部检测数据内存
[09-12 09:30:38.076] [debug] - 显存 - 目标跟踪完成: 使用 954.5 MB
[09-12 09:30:38.076] [debug] - 内存 - 目标跟踪完成: 使用 424.5 MB
[09-12 09:30:38.271] [debug] =====开始目标检测=====
[09-12 09:30:38.271] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.271] [info] 方位角变化，角度过滤，跳过帧: 2473，角度: 56.49，目标数量: 0, 列数据段数量: 0 
[09-12 09:30:38.271] [debug] =====开始目标跟踪======
[09-12 09:30:38.271] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.271] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.272] [debug] 跟踪结果数量: 0
[09-12 09:30:38.272] [info] Track[ 0] ID:  0 | Pos: ( 867.40,  429.20,   98.33) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.76 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.272] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.272] [debug] 开始释放检测结果数组
[09-12 09:30:38.272] [debug] 结束释放检测结果数组
[09-12 09:30:38.272] [debug] 开始释放列数据段
[09-12 09:30:38.272] [debug] 结束释放列数据段
[09-12 09:30:38.272] [debug] 已释放内部检测数据内存
[09-12 09:30:38.272] [debug] - 显存 - 目标跟踪完成: 使用 955.2 MB
[09-12 09:30:38.272] [debug] - 内存 - 目标跟踪完成: 使用 434.4 MB
[09-12 09:30:38.502] [debug] =====开始目标检测=====
[09-12 09:30:38.502] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.513] [warning] 无效帧:2572 模型没有检测到目标，跳过处理
[09-12 09:30:38.513] [debug] =====开始目标跟踪======
[09-12 09:30:38.513] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.513] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.514] [debug] 跟踪结果数量: 0
[09-12 09:30:38.514] [info] Track[ 0] ID:  0 | Pos: ( 867.82,  429.41,   98.38) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.24 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.514] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.514] [debug] 开始释放检测结果数组
[09-12 09:30:38.514] [debug] 结束释放检测结果数组
[09-12 09:30:38.514] [debug] 开始释放列数据段
[09-12 09:30:38.514] [debug] 结束释放列数据段
[09-12 09:30:38.514] [debug] 已释放内部检测数据内存
[09-12 09:30:38.514] [debug] - 显存 - 目标跟踪完成: 使用 955.5 MB
[09-12 09:30:38.514] [debug] - 内存 - 目标跟踪完成: 使用 455.7 MB
[09-12 09:30:38.753] [debug] =====开始目标检测=====
[09-12 09:30:38.754] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.763] [warning] 无效帧:2573 模型没有检测到目标，跳过处理
[09-12 09:30:38.763] [debug] =====开始目标跟踪======
[09-12 09:30:38.763] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.763] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.763] [debug] 跟踪结果数量: 0
[09-12 09:30:38.763] [info] Track[ 0] ID:  0 | Pos: ( 868.25,  429.62,   98.42) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.71 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.763] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.763] [debug] 开始释放检测结果数组
[09-12 09:30:38.763] [debug] 结束释放检测结果数组
[09-12 09:30:38.763] [debug] 开始释放列数据段
[09-12 09:30:38.763] [debug] 结束释放列数据段
[09-12 09:30:38.763] [debug] 已释放内部检测数据内存
[09-12 09:30:38.763] [debug] - 显存 - 目标跟踪完成: 使用 955.3 MB
[09-12 09:30:38.763] [debug] - 内存 - 目标跟踪完成: 使用 455.8 MB
[09-12 09:30:38.961] [debug] =====开始目标检测=====
[09-12 09:30:38.961] [debug] 提取到 1024 个有效帧头
[09-12 09:30:38.972] [warning] 无效帧:2574 模型没有检测到目标，跳过处理
[09-12 09:30:38.972] [debug] =====开始目标跟踪======
[09-12 09:30:38.972] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:38.972] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:38.972] [debug] 跟踪结果数量: 0
[09-12 09:30:38.972] [info] Track[ 0] ID:  0 | Pos: ( 868.67,  429.84,   98.47) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.19 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:38.972] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:38.972] [debug] 开始释放检测结果数组
[09-12 09:30:38.972] [debug] 结束释放检测结果数组
[09-12 09:30:38.972] [debug] 开始释放列数据段
[09-12 09:30:38.972] [debug] 结束释放列数据段
[09-12 09:30:38.972] [debug] 已释放内部检测数据内存
[09-12 09:30:38.972] [debug] - 显存 - 目标跟踪完成: 使用 955.4 MB
[09-12 09:30:38.972] [debug] - 内存 - 目标跟踪完成: 使用 456.0 MB
[09-12 09:30:39.919] [debug] =====开始目标检测=====
[09-12 09:30:39.919] [debug] 提取到 1024 个有效帧头
[09-12 09:30:39.931] [warning] 无效帧:2575 模型没有检测到目标，跳过处理
[09-12 09:30:39.931] [debug] =====开始目标跟踪======
[09-12 09:30:39.931] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:39.931] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:39.931] [debug] 跟踪结果数量: 0
[09-12 09:30:39.931] [info] Track[ 0] ID:  0 | Pos: ( 869.10,  430.05,   98.52) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.67 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:30:39.931] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:30:39.931] [debug] 开始释放检测结果数组
[09-12 09:30:39.931] [debug] 结束释放检测结果数组
[09-12 09:30:39.931] [debug] 开始释放列数据段
[09-12 09:30:39.931] [debug] 结束释放列数据段
[09-12 09:30:39.931] [debug] 已释放内部检测数据内存
[09-12 09:30:39.931] [debug] - 显存 - 目标跟踪完成: 使用 954.2 MB
[09-12 09:30:39.931] [debug] - 内存 - 目标跟踪完成: 使用 486.1 MB
[09-12 09:30:40.347] [debug] =====开始目标检测=====
[09-12 09:30:40.348] [debug] 提取到 1024 个有效帧头
[09-12 09:30:40.357] [warning] 无效帧:2576 模型没有检测到目标，跳过处理
[09-12 09:30:40.357] [debug] =====开始目标跟踪======
[09-12 09:30:40.357] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:30:40.357] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:30:40.358] [debug] 跟踪结果数量: 0
[09-12 09:30:40.358] [info] Track[ 0] ID:  0 | Pos: ( 869.52,  430.26,   98.57) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.14 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:34.582] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 09:41:34.582] [debug] =====算法库初始化开始=====
[09-12 09:41:34.582] [info] Initializing unified resource manager...
[09-12 09:41:34.583] [info] Initializing GPU resource manager...
[09-12 09:41:34.668] [debug] CUDA stream created: 0x555556068830
[09-12 09:41:34.668] [info] GPU resource manager initialized successfully
[09-12 09:41:34.668] [info] Initializing memory pool manager...
[09-12 09:41:34.668] [info] CUDA memory pool created with max size: 512MB
[09-12 09:41:34.668] [info] Host memory pool created with max size: 1024MB
[09-12 09:41:34.668] [info] Memory pool manager initialized successfully
[09-12 09:41:34.668] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-12 09:41:34.668] [info] Initializing FFT resource pools for 5 segment lengths
[09-12 09:41:34.668] [debug] Initializing resource pool for length: 512
[09-12 09:41:34.668] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a00000
[09-12 09:41:34.669] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.669] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a01200
[09-12 09:41:34.669] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.669] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a02400
[09-12 09:41:34.669] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.669] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a03600
[09-12 09:41:34.669] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.669] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a04800
[09-12 09:41:34.669] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.669] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a05a00
[09-12 09:41:34.669] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.669] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a06c00
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a07e00
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a09000
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a0a200
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:41:34.670] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-12 09:41:34.670] [debug] Initializing resource pool for length: 256
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0b400
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0be00
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0c800
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0d200
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0dc00
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0e600
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0f000
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0fa00
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10400
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10e00
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:41:34.670] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-12 09:41:34.670] [debug] Initializing resource pool for length: 128
[09-12 09:41:34.670] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11800
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11e00
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12400
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12a00
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13000
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13600
[09-12 09:41:34.670] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.670] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13c00
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14200
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14800
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14e00
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:41:34.671] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-12 09:41:34.671] [debug] Initializing resource pool for length: 64
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15400
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15800
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15c00
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16000
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16400
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16800
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16c00
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17000
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17400
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17800
[09-12 09:41:34.671] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:41:34.671] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-12 09:41:34.671] [info] All FFT resource pools initialized successfully
[09-12 09:41:34.671] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-12 09:41:34.672] [info] Unified resource manager initialized successfully
[09-12 09:41:34.672] [info] 重新配置日志系统 - 路径: logs/runtime.log, 等级: debug
[09-12 09:41:34.672] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 09:41:34.672] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-12 09:41:34.686] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-12 09:41:34.686] [info] Engine info: 2 bindings
[09-12 09:41:34.686] [info]   Binding 0: input [1x1x1024x1024x2]
[09-12 09:41:34.686] [info]   Binding 1: output [1x1x512x1024]
[09-12 09:41:34.686] [info] 模型输入维度: [1x1024x1024x2]
[09-12 09:41:34.686] [debug] CUDA memory allocated: 8388608 bytes at 0x7fff6a600000
[09-12 09:41:34.686] [debug] CUDA memory allocated: 2097152 bytes at 0x7fff6ae00000
[09-12 09:41:34.686] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-12 09:41:34.692] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-12 09:41:34.692] [debug] - 显存 - 算法初始化完成: 使用 969.1 MB
[09-12 09:41:34.692] [debug] - 内存 - 算法初始化完成: 使用 259.2 MB
[09-12 09:41:34.692] [debug] =====算法库初始化完成=====
[09-12 09:41:34.798] [debug] =====开始目标检测=====
[09-12 09:41:34.798] [debug] 提取到 1024 个有效帧头
[09-12 09:41:34.880] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 09:41:34.880] [debug] =====开始目标跟踪======
[09-12 09:41:34.880] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:34.880] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:34.880] [debug] 跟踪结果数量: 0
[09-12 09:41:34.880] [debug] 开始释放检测结果数组
[09-12 09:41:34.880] [debug] 结束释放检测结果数组
[09-12 09:41:34.880] [debug] 开始释放列数据段
[09-12 09:41:34.880] [debug] 结束释放列数据段
[09-12 09:41:34.880] [debug] 已释放内部检测数据内存
[09-12 09:41:34.882] [debug] - 显存 - 目标跟踪完成: 使用 969.1 MB
[09-12 09:41:34.882] [debug] - 内存 - 目标跟踪完成: 使用 484.9 MB
[09-12 09:41:34.929] [debug] =====开始目标检测=====
[09-12 09:41:34.930] [debug] 提取到 1024 个有效帧头
[09-12 09:41:34.937] [warning] 无效帧:2458 模型没有检测到目标，跳过处理
[09-12 09:41:34.937] [debug] =====开始目标跟踪======
[09-12 09:41:34.937] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:34.937] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:34.937] [debug] 跟踪结果数量: 0
[09-12 09:41:34.937] [debug] 开始释放检测结果数组
[09-12 09:41:34.937] [debug] 结束释放检测结果数组
[09-12 09:41:34.937] [debug] 开始释放列数据段
[09-12 09:41:34.937] [debug] 结束释放列数据段
[09-12 09:41:34.937] [debug] 已释放内部检测数据内存
[09-12 09:41:34.937] [debug] - 显存 - 目标跟踪完成: 使用 969.1 MB
[09-12 09:41:34.937] [debug] - 内存 - 目标跟踪完成: 使用 473.7 MB
[09-12 09:41:35.044] [debug] =====开始目标检测=====
[09-12 09:41:35.044] [debug] 提取到 1024 个有效帧头
[09-12 09:41:35.051] [warning] 无效帧:2459 模型没有检测到目标，跳过处理
[09-12 09:41:35.051] [debug] =====开始目标跟踪======
[09-12 09:41:35.051] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:35.051] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:35.051] [debug] 跟踪结果数量: 0
[09-12 09:41:35.051] [debug] 开始释放检测结果数组
[09-12 09:41:35.051] [debug] 结束释放检测结果数组
[09-12 09:41:35.051] [debug] 开始释放列数据段
[09-12 09:41:35.051] [debug] 结束释放列数据段
[09-12 09:41:35.051] [debug] 已释放内部检测数据内存
[09-12 09:41:35.051] [debug] - 显存 - 目标跟踪完成: 使用 968.6 MB
[09-12 09:41:35.051] [debug] - 内存 - 目标跟踪完成: 使用 459.7 MB
[09-12 09:41:35.157] [debug] =====开始目标检测=====
[09-12 09:41:35.157] [debug] 提取到 1024 个有效帧头
[09-12 09:41:35.168] [warning] 无效帧:2460 模型没有检测到目标，跳过处理
[09-12 09:41:35.168] [debug] =====开始目标跟踪======
[09-12 09:41:35.168] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:35.168] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:35.168] [debug] 跟踪结果数量: 0
[09-12 09:41:35.168] [debug] 开始释放检测结果数组
[09-12 09:41:35.168] [debug] 结束释放检测结果数组
[09-12 09:41:35.168] [debug] 开始释放列数据段
[09-12 09:41:35.168] [debug] 结束释放列数据段
[09-12 09:41:35.168] [debug] 已释放内部检测数据内存
[09-12 09:41:35.168] [debug] - 显存 - 目标跟踪完成: 使用 968.6 MB
[09-12 09:41:35.168] [debug] - 内存 - 目标跟踪完成: 使用 454.6 MB
[09-12 09:41:35.275] [debug] =====开始目标检测=====
[09-12 09:41:35.275] [debug] 提取到 1024 个有效帧头
[09-12 09:41:35.284] [warning] 无效帧:2461 模型没有检测到目标，跳过处理
[09-12 09:41:35.284] [debug] =====开始目标跟踪======
[09-12 09:41:35.284] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:35.284] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:35.284] [debug] 跟踪结果数量: 0
[09-12 09:41:35.284] [debug] 开始释放检测结果数组
[09-12 09:41:35.284] [debug] 结束释放检测结果数组
[09-12 09:41:35.284] [debug] 开始释放列数据段
[09-12 09:41:35.284] [debug] 结束释放列数据段
[09-12 09:41:35.284] [debug] 已释放内部检测数据内存
[09-12 09:41:35.286] [debug] - 显存 - 目标跟踪完成: 使用 968.4 MB
[09-12 09:41:35.286] [debug] - 内存 - 目标跟踪完成: 使用 416.7 MB
[09-12 09:41:35.395] [debug] =====开始目标检测=====
[09-12 09:41:35.395] [debug] 提取到 1024 个有效帧头
[09-12 09:41:35.408] [debug] 检测到 1 个目标中心点
[09-12 09:41:35.408] [debug] 开始提取列数据段，共 1 个中心点
[09-12 09:41:35.408] [debug] 检测点[0]: 帧号=2462, 列=1792, 行=565, 段长度=256
[09-12 09:41:35.408] [debug] 目标检测耗时(ms): 预处理:4 推理:2 后处理:5 提取:0 总:11 (FPS:90.91)
[09-12 09:41:35.408] [debug] =====开始目标跟踪======
[09-12 09:41:35.408] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 09:41:35.408] [debug] 处理检测点[0]: 列=1792, 行=565
[09-12 09:41:35.408] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:41:35.408] [debug] Released resources in pool (length 256), slot 0
[09-12 09:41:35.408] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:41:35.408] [debug] Released resources in pool (length 256), slot 0
[09-12 09:41:35.408] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 09:41:35.408] [info] Frame: 2462, Velo:   9.70, Range: 971.00, Amaz:  23.25, Elev:   7.20, Altitude: 121.70, X_cor: 1792, Y_cor: 565
[09-12 09:41:35.413] [debug] 跟踪结果数量: 0
[09-12 09:41:35.413] [debug] 开始释放检测结果数组
[09-12 09:41:35.413] [debug] 结束释放检测结果数组
[09-12 09:41:35.413] [debug] 开始释放列数据段
[09-12 09:41:35.413] [debug] 结束释放列数据段
[09-12 09:41:35.413] [debug] 已释放内部检测数据内存
[09-12 09:41:35.413] [debug] - 显存 - 目标跟踪完成: 使用 965.1 MB
[09-12 09:41:35.413] [debug] - 内存 - 目标跟踪完成: 使用 400.5 MB
[09-12 09:41:35.530] [debug] =====开始目标检测=====
[09-12 09:41:35.530] [debug] 提取到 1024 个有效帧头
[09-12 09:41:35.539] [debug] 检测到 1 个目标中心点
[09-12 09:41:35.539] [debug] 开始提取列数据段，共 1 个中心点
[09-12 09:41:35.539] [debug] 检测点[0]: 帧号=2463, 列=1791, 行=564, 段长度=256
[09-12 09:41:35.539] [debug] 目标检测耗时(ms): 预处理:4 推理:2 后处理:2 提取:0 总:8 (FPS:125.00)
[09-12 09:41:35.540] [debug] =====开始目标跟踪======
[09-12 09:41:35.540] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 09:41:35.540] [debug] 处理检测点[0]: 列=1791, 行=564
[09-12 09:41:35.540] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:41:35.540] [debug] Released resources in pool (length 256), slot 0
[09-12 09:41:35.540] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:41:35.540] [debug] Released resources in pool (length 256), slot 0
[09-12 09:41:35.540] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 09:41:35.540] [info] Frame: 2463, Velo:   9.52, Range: 968.00, Amaz:  26.33, Elev:   5.80, Altitude:  97.82, X_cor: 1791, Y_cor: 564
[09-12 09:41:35.543] [debug] 跟踪结果数量: 1
[09-12 09:41:35.543] [info] Track[ 0] ID:  0 | Pos: ( 863.15,  427.10,   97.85) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.00 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:35.543] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:35.543] [debug] 开始释放检测结果数组
[09-12 09:41:35.543] [debug] 结束释放检测结果数组
[09-12 09:41:35.543] [debug] 开始释放列数据段
[09-12 09:41:35.543] [debug] 结束释放列数据段
[09-12 09:41:35.543] [debug] 已释放内部检测数据内存
[09-12 09:41:35.543] [debug] - 显存 - 目标跟踪完成: 使用 965.6 MB
[09-12 09:41:35.543] [debug] - 内存 - 目标跟踪完成: 使用 396.8 MB
[09-12 09:41:35.646] [debug] =====开始目标检测=====
[09-12 09:41:35.646] [debug] 提取到 1024 个有效帧头
[09-12 09:41:35.656] [warning] 无效帧:2464 模型没有检测到目标，跳过处理
[09-12 09:41:35.657] [debug] =====开始目标跟踪======
[09-12 09:41:35.657] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:35.657] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:35.657] [debug] 跟踪结果数量: 0
[09-12 09:41:35.657] [info] Track[ 0] ID:  0 | Pos: ( 863.58,  427.31,   97.89) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.48 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:35.657] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:35.657] [debug] 开始释放检测结果数组
[09-12 09:41:35.657] [debug] 结束释放检测结果数组
[09-12 09:41:35.657] [debug] 开始释放列数据段
[09-12 09:41:35.657] [debug] 结束释放列数据段
[09-12 09:41:35.657] [debug] 已释放内部检测数据内存
[09-12 09:41:35.657] [debug] - 显存 - 目标跟踪完成: 使用 965.6 MB
[09-12 09:41:35.657] [debug] - 内存 - 目标跟踪完成: 使用 463.2 MB
[09-12 09:41:35.765] [debug] =====开始目标检测=====
[09-12 09:41:35.765] [debug] 提取到 1024 个有效帧头
[09-12 09:41:35.765] [info] 方位角变化，角度过滤，跳过帧: 2465，角度: 31.83，目标数量: 0, 列数据段数量: 0 
[09-12 09:41:35.765] [debug] =====开始目标跟踪======
[09-12 09:41:35.765] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:35.765] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:35.765] [debug] 跟踪结果数量: 0
[09-12 09:41:35.765] [info] Track[ 0] ID:  0 | Pos: ( 864.00,  427.52,   97.94) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.95 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:35.765] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:35.765] [debug] 开始释放检测结果数组
[09-12 09:41:35.765] [debug] 结束释放检测结果数组
[09-12 09:41:35.765] [debug] 开始释放列数据段
[09-12 09:41:35.765] [debug] 结束释放列数据段
[09-12 09:41:35.765] [debug] 已释放内部检测数据内存
[09-12 09:41:35.765] [debug] - 显存 - 目标跟踪完成: 使用 965.6 MB
[09-12 09:41:35.765] [debug] - 内存 - 目标跟踪完成: 使用 442.2 MB
[09-12 09:41:35.870] [debug] =====开始目标检测=====
[09-12 09:41:35.870] [debug] 提取到 1024 个有效帧头
[09-12 09:41:35.870] [info] 方位角变化，角度过滤，跳过帧: 2466，角度: 34.91，目标数量: 0, 列数据段数量: 0 
[09-12 09:41:35.870] [debug] =====开始目标跟踪======
[09-12 09:41:35.870] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:35.870] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:35.870] [debug] 跟踪结果数量: 0
[09-12 09:41:35.870] [info] Track[ 0] ID:  0 | Pos: ( 864.43,  427.73,   97.99) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.43 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:35.870] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:35.870] [debug] 开始释放检测结果数组
[09-12 09:41:35.870] [debug] 结束释放检测结果数组
[09-12 09:41:35.870] [debug] 开始释放列数据段
[09-12 09:41:35.870] [debug] 结束释放列数据段
[09-12 09:41:35.870] [debug] 已释放内部检测数据内存
[09-12 09:41:35.870] [debug] - 显存 - 目标跟踪完成: 使用 964.5 MB
[09-12 09:41:35.870] [debug] - 内存 - 目标跟踪完成: 使用 439.2 MB
[09-12 09:41:35.978] [debug] =====开始目标检测=====
[09-12 09:41:35.978] [debug] 提取到 1024 个有效帧头
[09-12 09:41:35.978] [info] 方位角变化，角度过滤，跳过帧: 2467，角度: 38.01，目标数量: 0, 列数据段数量: 0 
[09-12 09:41:35.979] [debug] =====开始目标跟踪======
[09-12 09:41:35.979] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:35.979] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:35.979] [debug] 跟踪结果数量: 0
[09-12 09:41:35.979] [info] Track[ 0] ID:  0 | Pos: ( 864.85,  427.94,   98.04) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.91 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:35.979] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:35.979] [debug] 开始释放检测结果数组
[09-12 09:41:35.979] [debug] 结束释放检测结果数组
[09-12 09:41:35.979] [debug] 开始释放列数据段
[09-12 09:41:35.979] [debug] 结束释放列数据段
[09-12 09:41:35.979] [debug] 已释放内部检测数据内存
[09-12 09:41:35.979] [debug] - 显存 - 目标跟踪完成: 使用 964.5 MB
[09-12 09:41:35.979] [debug] - 内存 - 目标跟踪完成: 使用 426.1 MB
[09-12 09:41:36.086] [debug] =====开始目标检测=====
[09-12 09:41:36.086] [debug] 提取到 1024 个有效帧头
[09-12 09:41:36.086] [info] 方位角变化，角度过滤，跳过帧: 2468，角度: 41.08，目标数量: 0, 列数据段数量: 0 
[09-12 09:41:36.086] [debug] =====开始目标跟踪======
[09-12 09:41:36.086] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:36.086] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:36.086] [debug] 跟踪结果数量: 0
[09-12 09:41:36.086] [info] Track[ 0] ID:  0 | Pos: ( 865.28,  428.15,   98.09) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.38 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:36.086] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:36.086] [debug] 开始释放检测结果数组
[09-12 09:41:36.086] [debug] 结束释放检测结果数组
[09-12 09:41:36.086] [debug] 开始释放列数据段
[09-12 09:41:36.086] [debug] 结束释放列数据段
[09-12 09:41:36.086] [debug] 已释放内部检测数据内存
[09-12 09:41:36.086] [debug] - 显存 - 目标跟踪完成: 使用 964.5 MB
[09-12 09:41:36.086] [debug] - 内存 - 目标跟踪完成: 使用 425.7 MB
[09-12 09:41:36.193] [debug] =====开始目标检测=====
[09-12 09:41:36.194] [debug] 提取到 1024 个有效帧头
[09-12 09:41:36.194] [info] 方位角变化，角度过滤，跳过帧: 2469，角度: 44.15，目标数量: 0, 列数据段数量: 0 
[09-12 09:41:36.194] [debug] =====开始目标跟踪======
[09-12 09:41:36.194] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:36.194] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:36.194] [debug] 跟踪结果数量: 0
[09-12 09:41:36.194] [info] Track[ 0] ID:  0 | Pos: ( 865.70,  428.36,   98.13) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.86 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:36.194] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:36.194] [debug] 开始释放检测结果数组
[09-12 09:41:36.194] [debug] 结束释放检测结果数组
[09-12 09:41:36.194] [debug] 开始释放列数据段
[09-12 09:41:36.194] [debug] 结束释放列数据段
[09-12 09:41:36.194] [debug] 已释放内部检测数据内存
[09-12 09:41:36.194] [debug] - 显存 - 目标跟踪完成: 使用 964.5 MB
[09-12 09:41:36.194] [debug] - 内存 - 目标跟踪完成: 使用 425.8 MB
[09-12 09:41:36.301] [debug] =====开始目标检测=====
[09-12 09:41:36.301] [debug] 提取到 1024 个有效帧头
[09-12 09:41:36.301] [info] 方位角变化，角度过滤，跳过帧: 2470，角度: 47.25，目标数量: 0, 列数据段数量: 0 
[09-12 09:41:36.302] [debug] =====开始目标跟踪======
[09-12 09:41:36.302] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:36.302] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:36.302] [debug] 跟踪结果数量: 0
[09-12 09:41:36.302] [info] Track[ 0] ID:  0 | Pos: ( 866.13,  428.57,   98.18) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.33 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:36.302] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:36.302] [debug] 开始释放检测结果数组
[09-12 09:41:36.302] [debug] 结束释放检测结果数组
[09-12 09:41:36.302] [debug] 开始释放列数据段
[09-12 09:41:36.302] [debug] 结束释放列数据段
[09-12 09:41:36.302] [debug] 已释放内部检测数据内存
[09-12 09:41:36.302] [debug] - 显存 - 目标跟踪完成: 使用 964.5 MB
[09-12 09:41:36.302] [debug] - 内存 - 目标跟踪完成: 使用 435.9 MB
[09-12 09:41:36.416] [debug] =====开始目标检测=====
[09-12 09:41:36.416] [debug] 提取到 1024 个有效帧头
[09-12 09:41:36.416] [info] 方位角变化，角度过滤，跳过帧: 2471，角度: 50.31，目标数量: 0, 列数据段数量: 0 
[09-12 09:41:36.416] [debug] =====开始目标跟踪======
[09-12 09:41:36.416] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:36.416] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:36.416] [debug] 跟踪结果数量: 0
[09-12 09:41:36.416] [info] Track[ 0] ID:  0 | Pos: ( 866.55,  428.78,   98.23) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.81 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:36.416] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:36.416] [debug] 开始释放检测结果数组
[09-12 09:41:36.416] [debug] 结束释放检测结果数组
[09-12 09:41:36.416] [debug] 开始释放列数据段
[09-12 09:41:36.416] [debug] 结束释放列数据段
[09-12 09:41:36.416] [debug] 已释放内部检测数据内存
[09-12 09:41:36.416] [debug] - 显存 - 目标跟踪完成: 使用 962.6 MB
[09-12 09:41:36.416] [debug] - 内存 - 目标跟踪完成: 使用 432.7 MB
[09-12 09:41:36.537] [debug] =====开始目标检测=====
[09-12 09:41:36.537] [debug] 提取到 1024 个有效帧头
[09-12 09:41:36.537] [info] 方位角变化，角度过滤，跳过帧: 2472，角度: 53.39，目标数量: 0, 列数据段数量: 0 
[09-12 09:41:36.537] [debug] =====开始目标跟踪======
[09-12 09:41:36.537] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:36.537] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:36.537] [debug] 跟踪结果数量: 0
[09-12 09:41:36.537] [info] Track[ 0] ID:  0 | Pos: ( 866.97,  428.99,   98.28) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.29 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:36.537] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:36.537] [debug] 开始释放检测结果数组
[09-12 09:41:36.537] [debug] 结束释放检测结果数组
[09-12 09:41:36.537] [debug] 开始释放列数据段
[09-12 09:41:36.537] [debug] 结束释放列数据段
[09-12 09:41:36.537] [debug] 已释放内部检测数据内存
[09-12 09:41:36.537] [debug] - 显存 - 目标跟踪完成: 使用 962.6 MB
[09-12 09:41:36.537] [debug] - 内存 - 目标跟踪完成: 使用 430.3 MB
[09-12 09:41:36.646] [debug] =====开始目标检测=====
[09-12 09:41:36.646] [debug] 提取到 1024 个有效帧头
[09-12 09:41:36.646] [info] 方位角变化，角度过滤，跳过帧: 2473，角度: 56.49，目标数量: 0, 列数据段数量: 0 
[09-12 09:41:36.646] [debug] =====开始目标跟踪======
[09-12 09:41:36.646] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:36.646] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:36.646] [debug] 跟踪结果数量: 0
[09-12 09:41:36.646] [info] Track[ 0] ID:  0 | Pos: ( 867.40,  429.20,   98.33) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.76 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:36.647] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:36.647] [debug] 开始释放检测结果数组
[09-12 09:41:36.647] [debug] 结束释放检测结果数组
[09-12 09:41:36.647] [debug] 开始释放列数据段
[09-12 09:41:36.647] [debug] 结束释放列数据段
[09-12 09:41:36.647] [debug] 已释放内部检测数据内存
[09-12 09:41:36.647] [debug] - 显存 - 目标跟踪完成: 使用 962.6 MB
[09-12 09:41:36.647] [debug] - 内存 - 目标跟踪完成: 使用 425.1 MB
[09-12 09:41:36.757] [debug] =====开始目标检测=====
[09-12 09:41:36.757] [debug] 提取到 1024 个有效帧头
[09-12 09:41:36.769] [warning] 无效帧:2572 模型没有检测到目标，跳过处理
[09-12 09:41:36.769] [debug] =====开始目标跟踪======
[09-12 09:41:36.769] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:36.769] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:36.769] [debug] 跟踪结果数量: 0
[09-12 09:41:36.769] [info] Track[ 0] ID:  0 | Pos: ( 867.82,  429.41,   98.38) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.24 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:36.769] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:36.769] [debug] 开始释放检测结果数组
[09-12 09:41:36.769] [debug] 结束释放检测结果数组
[09-12 09:41:36.769] [debug] 开始释放列数据段
[09-12 09:41:36.769] [debug] 结束释放列数据段
[09-12 09:41:36.769] [debug] 已释放内部检测数据内存
[09-12 09:41:36.769] [debug] - 显存 - 目标跟踪完成: 使用 962.7 MB
[09-12 09:41:36.769] [debug] - 内存 - 目标跟踪完成: 使用 471.3 MB
[09-12 09:41:36.870] [debug] =====开始目标检测=====
[09-12 09:41:36.870] [debug] 提取到 1024 个有效帧头
[09-12 09:41:36.878] [warning] 无效帧:2573 模型没有检测到目标，跳过处理
[09-12 09:41:36.880] [debug] =====开始目标跟踪======
[09-12 09:41:36.880] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:36.880] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:36.880] [debug] 跟踪结果数量: 0
[09-12 09:41:36.880] [info] Track[ 0] ID:  0 | Pos: ( 868.25,  429.62,   98.42) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.71 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:36.880] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:36.880] [debug] 开始释放检测结果数组
[09-12 09:41:36.880] [debug] 结束释放检测结果数组
[09-12 09:41:36.880] [debug] 开始释放列数据段
[09-12 09:41:36.880] [debug] 结束释放列数据段
[09-12 09:41:36.880] [debug] 已释放内部检测数据内存
[09-12 09:41:36.880] [debug] - 显存 - 目标跟踪完成: 使用 964.4 MB
[09-12 09:41:36.880] [debug] - 内存 - 目标跟踪完成: 使用 446.6 MB
[09-12 09:41:36.992] [debug] =====开始目标检测=====
[09-12 09:41:36.992] [debug] 提取到 1024 个有效帧头
[09-12 09:41:37.001] [warning] 无效帧:2574 模型没有检测到目标，跳过处理
[09-12 09:41:37.001] [debug] =====开始目标跟踪======
[09-12 09:41:37.001] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:37.001] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:37.002] [debug] 跟踪结果数量: 0
[09-12 09:41:37.002] [info] Track[ 0] ID:  0 | Pos: ( 868.67,  429.84,   98.47) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.19 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:37.002] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:37.002] [debug] 开始释放检测结果数组
[09-12 09:41:37.002] [debug] 结束释放检测结果数组
[09-12 09:41:37.002] [debug] 开始释放列数据段
[09-12 09:41:37.002] [debug] 结束释放列数据段
[09-12 09:41:37.002] [debug] 已释放内部检测数据内存
[09-12 09:41:37.002] [debug] - 显存 - 目标跟踪完成: 使用 964.7 MB
[09-12 09:41:37.002] [debug] - 内存 - 目标跟踪完成: 使用 477.5 MB
[09-12 09:41:37.107] [debug] =====开始目标检测=====
[09-12 09:41:37.107] [debug] 提取到 1024 个有效帧头
[09-12 09:41:37.114] [warning] 无效帧:2575 模型没有检测到目标，跳过处理
[09-12 09:41:37.114] [debug] =====开始目标跟踪======
[09-12 09:41:37.114] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:37.114] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:37.114] [debug] 跟踪结果数量: 0
[09-12 09:41:37.114] [info] Track[ 0] ID:  0 | Pos: ( 869.10,  430.05,   98.52) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.67 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:37.114] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:37.114] [debug] 开始释放检测结果数组
[09-12 09:41:37.114] [debug] 结束释放检测结果数组
[09-12 09:41:37.114] [debug] 开始释放列数据段
[09-12 09:41:37.114] [debug] 结束释放列数据段
[09-12 09:41:37.114] [debug] 已释放内部检测数据内存
[09-12 09:41:37.114] [debug] - 显存 - 目标跟踪完成: 使用 964.7 MB
[09-12 09:41:37.114] [debug] - 内存 - 目标跟踪完成: 使用 446.9 MB
[09-12 09:41:37.222] [debug] =====开始目标检测=====
[09-12 09:41:37.222] [debug] 提取到 1024 个有效帧头
[09-12 09:41:37.231] [warning] 无效帧:2576 模型没有检测到目标，跳过处理
[09-12 09:41:37.232] [debug] =====开始目标跟踪======
[09-12 09:41:37.232] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:37.232] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:37.232] [debug] 跟踪结果数量: 0
[09-12 09:41:37.232] [info] Track[ 0] ID:  0 | Pos: ( 869.52,  430.26,   98.57) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.14 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:41:37.232] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:41:37.232] [debug] 开始释放检测结果数组
[09-12 09:41:37.232] [debug] 结束释放检测结果数组
[09-12 09:41:37.232] [debug] 开始释放列数据段
[09-12 09:41:37.232] [debug] 结束释放列数据段
[09-12 09:41:37.232] [debug] 已释放内部检测数据内存
[09-12 09:41:37.232] [debug] - 显存 - 目标跟踪完成: 使用 964.7 MB
[09-12 09:41:37.232] [debug] - 内存 - 目标跟踪完成: 使用 455.2 MB
[09-12 09:41:37.348] [debug] =====开始目标检测=====
[09-12 09:41:37.348] [debug] 提取到 1024 个有效帧头
[09-12 09:41:37.359] [warning] 无效帧:2577 模型没有检测到目标，跳过处理
[09-12 09:41:37.360] [debug] =====开始目标跟踪======
[09-12 09:41:37.360] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:41:37.360] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:41:37.360] [debug] 跟踪结果数量: 0
[09-12 09:41:37.360] [info] Track[ 0] ID:  0 | Pos: ( 869.95,  430.47,   98.62) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.62 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:33.793] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 09:42:33.794] [debug] =====算法库初始化开始=====
[09-12 09:42:33.794] [info] Initializing unified resource manager...
[09-12 09:42:33.794] [info] Initializing GPU resource manager...
[09-12 09:42:33.872] [debug] CUDA stream created: 0x555556068830
[09-12 09:42:33.872] [info] GPU resource manager initialized successfully
[09-12 09:42:33.872] [info] Initializing memory pool manager...
[09-12 09:42:33.872] [info] CUDA memory pool created with max size: 512MB
[09-12 09:42:33.872] [info] Host memory pool created with max size: 1024MB
[09-12 09:42:33.872] [info] Memory pool manager initialized successfully
[09-12 09:42:33.872] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-12 09:42:33.872] [info] Initializing FFT resource pools for 5 segment lengths
[09-12 09:42:33.872] [debug] Initializing resource pool for length: 512
[09-12 09:42:33.872] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a00000
[09-12 09:42:33.873] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.873] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a01200
[09-12 09:42:33.873] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.873] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a02400
[09-12 09:42:33.873] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.873] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a03600
[09-12 09:42:33.873] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.873] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a04800
[09-12 09:42:33.873] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.873] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a05a00
[09-12 09:42:33.873] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.873] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a06c00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a07e00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a09000
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a0a200
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 09:42:33.874] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-12 09:42:33.874] [debug] Initializing resource pool for length: 256
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0b400
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0be00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0c800
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0d200
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0dc00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0e600
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0f000
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0fa00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10400
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10e00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 09:42:33.874] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-12 09:42:33.874] [debug] Initializing resource pool for length: 128
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11800
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11e00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12400
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12a00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13000
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13600
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13c00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14200
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14800
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14e00
[09-12 09:42:33.874] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 09:42:33.874] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-12 09:42:33.874] [debug] Initializing resource pool for length: 64
[09-12 09:42:33.874] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15400
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15800
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15c00
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16000
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16400
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16800
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16c00
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17000
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17400
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17800
[09-12 09:42:33.875] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 09:42:33.875] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-12 09:42:33.875] [info] All FFT resource pools initialized successfully
[09-12 09:42:33.875] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-12 09:42:33.875] [info] Unified resource manager initialized successfully
[09-12 09:42:33.875] [info] 重新配置日志系统 - 路径: logs/runtime.log, 等级: debug
[09-12 09:42:33.875] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 09:42:33.875] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-12 09:42:33.889] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-12 09:42:33.889] [info] Engine info: 2 bindings
[09-12 09:42:33.889] [info]   Binding 0: input [1x1x1024x1024x2]
[09-12 09:42:33.889] [info]   Binding 1: output [1x1x512x1024]
[09-12 09:42:33.889] [info] 模型输入维度: [1x1024x1024x2]
[09-12 09:42:33.890] [debug] CUDA memory allocated: 8388608 bytes at 0x7fff6a600000
[09-12 09:42:33.890] [debug] CUDA memory allocated: 2097152 bytes at 0x7fff6ae00000
[09-12 09:42:33.890] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-12 09:42:33.895] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-12 09:42:33.895] [debug] - 显存 - 算法初始化完成: 使用 955.8 MB
[09-12 09:42:33.895] [debug] - 内存 - 算法初始化完成: 使用 259.0 MB
[09-12 09:42:33.895] [debug] =====算法库初始化完成=====
[09-12 09:42:34.014] [debug] =====开始目标检测=====
[09-12 09:42:34.014] [debug] 提取到 1024 个有效帧头
[09-12 09:42:34.091] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 09:42:34.092] [debug] =====开始目标跟踪======
[09-12 09:42:34.092] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:34.092] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:34.092] [debug] 跟踪结果数量: 0
[09-12 09:42:34.092] [debug] 开始释放检测结果数组
[09-12 09:42:34.092] [debug] 结束释放检测结果数组
[09-12 09:42:34.092] [debug] 开始释放列数据段
[09-12 09:42:34.092] [debug] 结束释放列数据段
[09-12 09:42:34.092] [debug] 已释放内部检测数据内存
[09-12 09:42:34.094] [debug] - 显存 - 目标跟踪完成: 使用 956.6 MB
[09-12 09:42:34.094] [debug] - 内存 - 目标跟踪完成: 使用 460.7 MB
[09-12 09:42:34.140] [debug] =====开始目标检测=====
[09-12 09:42:34.140] [debug] 提取到 1024 个有效帧头
[09-12 09:42:34.151] [warning] 无效帧:2458 模型没有检测到目标，跳过处理
[09-12 09:42:34.151] [debug] =====开始目标跟踪======
[09-12 09:42:34.151] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:34.151] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:34.151] [debug] 跟踪结果数量: 0
[09-12 09:42:34.151] [debug] 开始释放检测结果数组
[09-12 09:42:34.151] [debug] 结束释放检测结果数组
[09-12 09:42:34.151] [debug] 开始释放列数据段
[09-12 09:42:34.151] [debug] 结束释放列数据段
[09-12 09:42:34.151] [debug] 已释放内部检测数据内存
[09-12 09:42:34.151] [debug] - 显存 - 目标跟踪完成: 使用 955.7 MB
[09-12 09:42:34.151] [debug] - 内存 - 目标跟踪完成: 使用 480.3 MB
[09-12 09:42:34.259] [debug] =====开始目标检测=====
[09-12 09:42:34.259] [debug] 提取到 1024 个有效帧头
[09-12 09:42:34.266] [warning] 无效帧:2459 模型没有检测到目标，跳过处理
[09-12 09:42:34.268] [debug] =====开始目标跟踪======
[09-12 09:42:34.268] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:34.268] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:34.268] [debug] 跟踪结果数量: 0
[09-12 09:42:34.268] [debug] 开始释放检测结果数组
[09-12 09:42:34.268] [debug] 结束释放检测结果数组
[09-12 09:42:34.268] [debug] 开始释放列数据段
[09-12 09:42:34.268] [debug] 结束释放列数据段
[09-12 09:42:34.268] [debug] 已释放内部检测数据内存
[09-12 09:42:34.268] [debug] - 显存 - 目标跟踪完成: 使用 955.7 MB
[09-12 09:42:34.268] [debug] - 内存 - 目标跟踪完成: 使用 471.5 MB
[09-12 09:42:34.369] [debug] =====开始目标检测=====
[09-12 09:42:34.369] [debug] 提取到 1024 个有效帧头
[09-12 09:42:34.381] [warning] 无效帧:2460 模型没有检测到目标，跳过处理
[09-12 09:42:34.381] [debug] =====开始目标跟踪======
[09-12 09:42:34.381] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:34.381] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:34.381] [debug] 跟踪结果数量: 0
[09-12 09:42:34.381] [debug] 开始释放检测结果数组
[09-12 09:42:34.381] [debug] 结束释放检测结果数组
[09-12 09:42:34.381] [debug] 开始释放列数据段
[09-12 09:42:34.381] [debug] 结束释放列数据段
[09-12 09:42:34.381] [debug] 已释放内部检测数据内存
[09-12 09:42:34.381] [debug] - 显存 - 目标跟踪完成: 使用 955.7 MB
[09-12 09:42:34.381] [debug] - 内存 - 目标跟踪完成: 使用 466.6 MB
[09-12 09:42:34.482] [debug] =====开始目标检测=====
[09-12 09:42:34.482] [debug] 提取到 1024 个有效帧头
[09-12 09:42:34.494] [warning] 无效帧:2461 模型没有检测到目标，跳过处理
[09-12 09:42:34.494] [debug] =====开始目标跟踪======
[09-12 09:42:34.494] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:34.494] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:34.494] [debug] 跟踪结果数量: 0
[09-12 09:42:34.494] [debug] 开始释放检测结果数组
[09-12 09:42:34.494] [debug] 结束释放检测结果数组
[09-12 09:42:34.494] [debug] 开始释放列数据段
[09-12 09:42:34.494] [debug] 结束释放列数据段
[09-12 09:42:34.494] [debug] 已释放内部检测数据内存
[09-12 09:42:34.494] [debug] - 显存 - 目标跟踪完成: 使用 955.4 MB
[09-12 09:42:34.494] [debug] - 内存 - 目标跟踪完成: 使用 480.5 MB
[09-12 09:42:34.595] [debug] =====开始目标检测=====
[09-12 09:42:34.595] [debug] 提取到 1024 个有效帧头
[09-12 09:42:34.603] [debug] 检测到 1 个目标中心点
[09-12 09:42:34.603] [debug] 开始提取列数据段，共 1 个中心点
[09-12 09:42:34.603] [debug] 检测点[0]: 帧号=2462, 列=1792, 行=565, 段长度=256
[09-12 09:42:34.603] [debug] 目标检测耗时(ms): 预处理:3 推理:2 后处理:2 提取:0 总:7 (FPS:142.86)
[09-12 09:42:34.603] [debug] =====开始目标跟踪======
[09-12 09:42:34.603] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 09:42:34.603] [debug] 处理检测点[0]: 列=1792, 行=565
[09-12 09:42:34.603] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:42:34.604] [debug] Released resources in pool (length 256), slot 0
[09-12 09:42:34.604] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:42:34.604] [debug] Released resources in pool (length 256), slot 0
[09-12 09:42:34.604] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 09:42:34.604] [info] Frame: 2462, Velo:   9.70, Range: 971.00, Amaz:  23.25, Elev:   7.20, Altitude: 121.70, X_cor: 1792, Y_cor: 565
[09-12 09:42:34.607] [debug] 跟踪结果数量: 0
[09-12 09:42:34.607] [debug] 开始释放检测结果数组
[09-12 09:42:34.607] [debug] 结束释放检测结果数组
[09-12 09:42:34.607] [debug] 开始释放列数据段
[09-12 09:42:34.607] [debug] 结束释放列数据段
[09-12 09:42:34.607] [debug] 已释放内部检测数据内存
[09-12 09:42:34.607] [debug] - 显存 - 目标跟踪完成: 使用 956.4 MB
[09-12 09:42:34.607] [debug] - 内存 - 目标跟踪完成: 使用 396.7 MB
[09-12 09:42:34.707] [debug] =====开始目标检测=====
[09-12 09:42:34.707] [debug] 提取到 1024 个有效帧头
[09-12 09:42:34.716] [debug] 检测到 1 个目标中心点
[09-12 09:42:34.716] [debug] 开始提取列数据段，共 1 个中心点
[09-12 09:42:34.716] [debug] 检测点[0]: 帧号=2463, 列=1791, 行=564, 段长度=256
[09-12 09:42:34.716] [debug] 目标检测耗时(ms): 预处理:4 推理:2 后处理:1 提取:0 总:7 (FPS:142.86)
[09-12 09:42:34.716] [debug] =====开始目标跟踪======
[09-12 09:42:34.716] [debug] 检测数据: num_detections=1, num_segments=1
[09-12 09:42:34.716] [debug] 处理检测点[0]: 列=1791, 行=564
[09-12 09:42:34.716] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:42:34.716] [debug] Released resources in pool (length 256), slot 0
[09-12 09:42:34.716] [debug] Acquired resources from pool 1 (length 256), slot 0
[09-12 09:42:34.716] [debug] Released resources in pool (length 256), slot 0
[09-12 09:42:34.716] [debug] FFT和俯仰角计算完成, 得到 1 个结果：
[09-12 09:42:34.716] [info] Frame: 2463, Velo:   9.52, Range: 968.00, Amaz:  26.33, Elev:   5.80, Altitude:  97.82, X_cor: 1791, Y_cor: 564
[09-12 09:42:34.720] [debug] 跟踪结果数量: 1
[09-12 09:42:34.720] [info] Track[ 0] ID:  0 | Pos: ( 863.15,  427.10,   97.85) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.00 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:34.720] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:34.720] [debug] 开始释放检测结果数组
[09-12 09:42:34.720] [debug] 结束释放检测结果数组
[09-12 09:42:34.720] [debug] 开始释放列数据段
[09-12 09:42:34.720] [debug] 结束释放列数据段
[09-12 09:42:34.720] [debug] 已释放内部检测数据内存
[09-12 09:42:34.720] [debug] - 显存 - 目标跟踪完成: 使用 955.4 MB
[09-12 09:42:34.720] [debug] - 内存 - 目标跟踪完成: 使用 396.7 MB
[09-12 09:42:34.819] [debug] =====开始目标检测=====
[09-12 09:42:34.819] [debug] 提取到 1024 个有效帧头
[09-12 09:42:34.831] [warning] 无效帧:2464 模型没有检测到目标，跳过处理
[09-12 09:42:34.832] [debug] =====开始目标跟踪======
[09-12 09:42:34.832] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:34.832] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:34.832] [debug] 跟踪结果数量: 0
[09-12 09:42:34.832] [info] Track[ 0] ID:  0 | Pos: ( 863.58,  427.31,   97.89) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.48 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:34.832] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:34.832] [debug] 开始释放检测结果数组
[09-12 09:42:34.832] [debug] 结束释放检测结果数组
[09-12 09:42:34.832] [debug] 开始释放列数据段
[09-12 09:42:34.832] [debug] 结束释放列数据段
[09-12 09:42:34.832] [debug] 已释放内部检测数据内存
[09-12 09:42:34.832] [debug] - 显存 - 目标跟踪完成: 使用 955.4 MB
[09-12 09:42:34.832] [debug] - 内存 - 目标跟踪完成: 使用 417.1 MB
[09-12 09:42:34.935] [debug] =====开始目标检测=====
[09-12 09:42:34.935] [debug] 提取到 1024 个有效帧头
[09-12 09:42:34.935] [info] 方位角变化，角度过滤，跳过帧: 2465，角度: 31.83，目标数量: 0, 列数据段数量: 0 
[09-12 09:42:34.935] [debug] =====开始目标跟踪======
[09-12 09:42:34.935] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:34.935] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:34.935] [debug] 跟踪结果数量: 0
[09-12 09:42:34.935] [info] Track[ 0] ID:  0 | Pos: ( 864.00,  427.52,   97.94) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.95 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:34.935] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:34.935] [debug] 开始释放检测结果数组
[09-12 09:42:34.936] [debug] 结束释放检测结果数组
[09-12 09:42:34.936] [debug] 开始释放列数据段
[09-12 09:42:34.936] [debug] 结束释放列数据段
[09-12 09:42:34.936] [debug] 已释放内部检测数据内存
[09-12 09:42:34.936] [debug] - 显存 - 目标跟踪完成: 使用 955.4 MB
[09-12 09:42:34.936] [debug] - 内存 - 目标跟踪完成: 使用 425.6 MB
[09-12 09:42:35.047] [debug] =====开始目标检测=====
[09-12 09:42:35.047] [debug] 提取到 1024 个有效帧头
[09-12 09:42:35.047] [info] 方位角变化，角度过滤，跳过帧: 2466，角度: 34.91，目标数量: 0, 列数据段数量: 0 
[09-12 09:42:35.047] [debug] =====开始目标跟踪======
[09-12 09:42:35.047] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:35.047] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:35.047] [debug] 跟踪结果数量: 0
[09-12 09:42:35.047] [info] Track[ 0] ID:  0 | Pos: ( 864.43,  427.73,   97.99) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.43 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:35.047] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:35.047] [debug] 开始释放检测结果数组
[09-12 09:42:35.047] [debug] 结束释放检测结果数组
[09-12 09:42:35.047] [debug] 开始释放列数据段
[09-12 09:42:35.047] [debug] 结束释放列数据段
[09-12 09:42:35.047] [debug] 已释放内部检测数据内存
[09-12 09:42:35.047] [debug] - 显存 - 目标跟踪完成: 使用 955.4 MB
[09-12 09:42:35.047] [debug] - 内存 - 目标跟踪完成: 使用 432.8 MB
[09-12 09:42:35.156] [debug] =====开始目标检测=====
[09-12 09:42:35.156] [debug] 提取到 1024 个有效帧头
[09-12 09:42:35.156] [info] 方位角变化，角度过滤，跳过帧: 2467，角度: 38.01，目标数量: 0, 列数据段数量: 0 
[09-12 09:42:35.156] [debug] =====开始目标跟踪======
[09-12 09:42:35.156] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:35.156] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:35.156] [debug] 跟踪结果数量: 0
[09-12 09:42:35.156] [info] Track[ 0] ID:  0 | Pos: ( 864.85,  427.94,   98.04) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.91 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:35.156] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:35.156] [debug] 开始释放检测结果数组
[09-12 09:42:35.156] [debug] 结束释放检测结果数组
[09-12 09:42:35.156] [debug] 开始释放列数据段
[09-12 09:42:35.156] [debug] 结束释放列数据段
[09-12 09:42:35.156] [debug] 已释放内部检测数据内存
[09-12 09:42:35.156] [debug] - 显存 - 目标跟踪完成: 使用 953.6 MB
[09-12 09:42:35.156] [debug] - 内存 - 目标跟踪完成: 使用 448.2 MB
[09-12 09:42:35.264] [debug] =====开始目标检测=====
[09-12 09:42:35.264] [debug] 提取到 1024 个有效帧头
[09-12 09:42:35.264] [info] 方位角变化，角度过滤，跳过帧: 2468，角度: 41.08，目标数量: 0, 列数据段数量: 0 
[09-12 09:42:35.264] [debug] =====开始目标跟踪======
[09-12 09:42:35.264] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:35.264] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:35.264] [debug] 跟踪结果数量: 0
[09-12 09:42:35.264] [info] Track[ 0] ID:  0 | Pos: ( 865.28,  428.15,   98.09) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.38 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:35.264] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:35.264] [debug] 开始释放检测结果数组
[09-12 09:42:35.264] [debug] 结束释放检测结果数组
[09-12 09:42:35.264] [debug] 开始释放列数据段
[09-12 09:42:35.264] [debug] 结束释放列数据段
[09-12 09:42:35.264] [debug] 已释放内部检测数据内存
[09-12 09:42:35.264] [debug] - 显存 - 目标跟踪完成: 使用 953.8 MB
[09-12 09:42:35.264] [debug] - 内存 - 目标跟踪完成: 使用 442.2 MB
[09-12 09:42:35.372] [debug] =====开始目标检测=====
[09-12 09:42:35.372] [debug] 提取到 1024 个有效帧头
[09-12 09:42:35.372] [info] 方位角变化，角度过滤，跳过帧: 2469，角度: 44.15，目标数量: 0, 列数据段数量: 0 
[09-12 09:42:35.372] [debug] =====开始目标跟踪======
[09-12 09:42:35.372] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:35.372] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:35.372] [debug] 跟踪结果数量: 0
[09-12 09:42:35.372] [info] Track[ 0] ID:  0 | Pos: ( 865.70,  428.36,   98.13) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.86 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:35.372] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:35.372] [debug] 开始释放检测结果数组
[09-12 09:42:35.372] [debug] 结束释放检测结果数组
[09-12 09:42:35.372] [debug] 开始释放列数据段
[09-12 09:42:35.372] [debug] 结束释放列数据段
[09-12 09:42:35.372] [debug] 已释放内部检测数据内存
[09-12 09:42:35.373] [debug] - 显存 - 目标跟踪完成: 使用 953.8 MB
[09-12 09:42:35.373] [debug] - 内存 - 目标跟踪完成: 使用 446.7 MB
[09-12 09:42:35.481] [debug] =====开始目标检测=====
[09-12 09:42:35.481] [debug] 提取到 1024 个有效帧头
[09-12 09:42:35.481] [info] 方位角变化，角度过滤，跳过帧: 2470，角度: 47.25，目标数量: 0, 列数据段数量: 0 
[09-12 09:42:35.481] [debug] =====开始目标跟踪======
[09-12 09:42:35.481] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:35.481] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:35.481] [debug] 跟踪结果数量: 0
[09-12 09:42:35.481] [info] Track[ 0] ID:  0 | Pos: ( 866.13,  428.57,   98.18) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.33 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:35.481] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:35.481] [debug] 开始释放检测结果数组
[09-12 09:42:35.481] [debug] 结束释放检测结果数组
[09-12 09:42:35.481] [debug] 开始释放列数据段
[09-12 09:42:35.481] [debug] 结束释放列数据段
[09-12 09:42:35.481] [debug] 已释放内部检测数据内存
[09-12 09:42:35.481] [debug] - 显存 - 目标跟踪完成: 使用 954.6 MB
[09-12 09:42:35.481] [debug] - 内存 - 目标跟踪完成: 使用 434.7 MB
[09-12 09:42:35.593] [debug] =====开始目标检测=====
[09-12 09:42:35.593] [debug] 提取到 1024 个有效帧头
[09-12 09:42:35.593] [info] 方位角变化，角度过滤，跳过帧: 2471，角度: 50.31，目标数量: 0, 列数据段数量: 0 
[09-12 09:42:35.593] [debug] =====开始目标跟踪======
[09-12 09:42:35.593] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:35.593] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:35.593] [debug] 跟踪结果数量: 0
[09-12 09:42:35.593] [info] Track[ 0] ID:  0 | Pos: ( 866.55,  428.78,   98.23) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.81 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:35.593] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:35.593] [debug] 开始释放检测结果数组
[09-12 09:42:35.593] [debug] 结束释放检测结果数组
[09-12 09:42:35.593] [debug] 开始释放列数据段
[09-12 09:42:35.593] [debug] 结束释放列数据段
[09-12 09:42:35.593] [debug] 已释放内部检测数据内存
[09-12 09:42:35.593] [debug] - 显存 - 目标跟踪完成: 使用 954.6 MB
[09-12 09:42:35.593] [debug] - 内存 - 目标跟踪完成: 使用 428.7 MB
[09-12 09:42:35.700] [debug] =====开始目标检测=====
[09-12 09:42:35.700] [debug] 提取到 1024 个有效帧头
[09-12 09:42:35.700] [info] 方位角变化，角度过滤，跳过帧: 2472，角度: 53.39，目标数量: 0, 列数据段数量: 0 
[09-12 09:42:35.700] [debug] =====开始目标跟踪======
[09-12 09:42:35.700] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:35.700] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:35.700] [debug] 跟踪结果数量: 0
[09-12 09:42:35.700] [info] Track[ 0] ID:  0 | Pos: ( 866.97,  428.99,   98.28) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.29 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 09:42:35.700] [debug] 跟踪完成，输出 1 个轨迹
[09-12 09:42:35.700] [debug] 开始释放检测结果数组
[09-12 09:42:35.700] [debug] 结束释放检测结果数组
[09-12 09:42:35.700] [debug] 开始释放列数据段
[09-12 09:42:35.700] [debug] 结束释放列数据段
[09-12 09:42:35.700] [debug] 已释放内部检测数据内存
[09-12 09:42:35.700] [debug] - 显存 - 目标跟踪完成: 使用 954.6 MB
[09-12 09:42:35.700] [debug] - 内存 - 目标跟踪完成: 使用 432.5 MB
[09-12 09:42:35.808] [debug] =====开始目标检测=====
[09-12 09:42:35.808] [debug] 提取到 1024 个有效帧头
[09-12 09:42:35.808] [info] 方位角变化，角度过滤，跳过帧: 2473，角度: 56.49，目标数量: 0, 列数据段数量: 0 
[09-12 09:42:35.808] [debug] =====开始目标跟踪======
[09-12 09:42:35.808] [debug] 检测数据: num_detections=0, num_segments=0
[09-12 09:42:35.808] [debug] FFT和俯仰角计算完成, 得到 0 个结果：
[09-12 09:42:35.808] [debug] 跟踪结果数量: 0
[09-12 09:42:35.808] [info] Track[ 0] ID:  0 | Pos: ( 867.40,  429.20,   98.33) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.76 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:22:07.539] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 17:22:07.547] [debug] =====算法库初始化开始=====
[09-12 17:22:07.547] [info] Initializing unified resource manager...
[09-12 17:22:07.547] [info] Initializing GPU resource manager...
[09-12 17:22:07.698] [debug] CUDA stream created: 0x555556068830
[09-12 17:22:07.698] [info] GPU resource manager initialized successfully
[09-12 17:22:07.698] [info] Initializing memory pool manager...
[09-12 17:22:07.698] [info] CUDA memory pool created with max size: 512MB
[09-12 17:22:07.698] [info] Host memory pool created with max size: 1024MB
[09-12 17:22:07.698] [info] Memory pool manager initialized successfully
[09-12 17:22:07.698] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-12 17:22:07.698] [info] Initializing FFT resource pools for 5 segment lengths
[09-12 17:22:07.698] [debug] Initializing resource pool for length: 512
[09-12 17:22:07.698] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a00000
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a01200
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a02400
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a03600
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a04800
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a05a00
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a06c00
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a07e00
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a09000
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a0a200
[09-12 17:22:07.739] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:07.739] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-12 17:22:07.739] [debug] Initializing resource pool for length: 256
[09-12 17:22:07.739] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0b400
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0be00
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0c800
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0d200
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0dc00
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0e600
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0f000
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0fa00
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10400
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10e00
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:07.740] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-12 17:22:07.740] [debug] Initializing resource pool for length: 128
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11800
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11e00
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12400
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12a00
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13000
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13600
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13c00
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14200
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14800
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14e00
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:07.740] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-12 17:22:07.740] [debug] Initializing resource pool for length: 64
[09-12 17:22:07.740] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15400
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15800
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15c00
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.740] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16000
[09-12 17:22:07.740] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.741] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16400
[09-12 17:22:07.741] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.741] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16800
[09-12 17:22:07.741] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.741] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16c00
[09-12 17:22:07.741] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.741] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17000
[09-12 17:22:07.741] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.741] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17400
[09-12 17:22:07.741] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.741] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17800
[09-12 17:22:07.741] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:07.741] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-12 17:22:07.741] [info] All FFT resource pools initialized successfully
[09-12 17:22:07.741] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-12 17:22:07.741] [info] Unified resource manager initialized successfully
[09-12 17:22:07.741] [info] 重新配置日志系统 - 路径: logs/runtime.log, 等级: debug
[09-12 17:22:07.741] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 17:22:07.741] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-12 17:22:07.755] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-12 17:22:07.755] [info] Engine info: 2 bindings
[09-12 17:22:07.755] [info]   Binding 0: input [1x1x1024x1024x2]
[09-12 17:22:07.755] [info]   Binding 1: output [1x1x512x1024]
[09-12 17:22:07.755] [info] 模型输入维度: [1x1024x1024x2]
[09-12 17:22:07.755] [debug] CUDA memory allocated: 8388608 bytes at 0x7fff6a600000
[09-12 17:22:07.755] [debug] CUDA memory allocated: 2097152 bytes at 0x7fff6ae00000
[09-12 17:22:07.755] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-12 17:22:07.761] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-12 17:22:07.772] [debug] - 显存 - 算法初始化完成: 使用 915.6 MB
[09-12 17:22:07.773] [debug] - 内存 - 算法初始化完成: 使用 258.8 MB
[09-12 17:22:07.773] [debug] =====算法库初始化完成=====
[09-12 17:22:08.579] [debug] =====开始目标检测=====
[09-12 17:22:08.579] [debug] 提取到 1024 个有效帧头
[09-12 17:22:08.660] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 17:22:34.022] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 17:22:34.022] [debug] =====算法库初始化开始=====
[09-12 17:22:34.022] [info] Initializing unified resource manager...
[09-12 17:22:34.023] [info] Initializing GPU resource manager...
[09-12 17:22:34.100] [debug] CUDA stream created: 0x555556068830
[09-12 17:22:34.100] [info] GPU resource manager initialized successfully
[09-12 17:22:34.100] [info] Initializing memory pool manager...
[09-12 17:22:34.100] [info] CUDA memory pool created with max size: 512MB
[09-12 17:22:34.100] [info] Host memory pool created with max size: 1024MB
[09-12 17:22:34.100] [info] Memory pool manager initialized successfully
[09-12 17:22:34.100] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-12 17:22:34.100] [info] Initializing FFT resource pools for 5 segment lengths
[09-12 17:22:34.100] [debug] Initializing resource pool for length: 512
[09-12 17:22:34.100] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a00000
[09-12 17:22:34.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a01200
[09-12 17:22:34.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a02400
[09-12 17:22:34.101] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.101] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a03600
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a04800
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a05a00
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a06c00
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a07e00
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a09000
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 4096 bytes at 0x7fff71a0a200
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=512, type=41, batch=1
[09-12 17:22:34.102] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-12 17:22:34.102] [debug] Initializing resource pool for length: 256
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0b400
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0be00
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0c800
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0d200
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0dc00
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0e600
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0f000
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a0fa00
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10400
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 2048 bytes at 0x7fff71a10e00
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=256, type=41, batch=1
[09-12 17:22:34.102] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-12 17:22:34.102] [debug] Initializing resource pool for length: 128
[09-12 17:22:34.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11800
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a11e00
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12400
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a12a00
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13000
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13600
[09-12 17:22:34.102] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.102] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a13c00
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14200
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14800
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 1024 bytes at 0x7fff71a14e00
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=128, type=41, batch=1
[09-12 17:22:34.103] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-12 17:22:34.103] [debug] Initializing resource pool for length: 64
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15400
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15800
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a15c00
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16000
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16400
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16800
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a16c00
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17000
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17400
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [debug] CUDA memory allocated: 512 bytes at 0x7fff71a17800
[09-12 17:22:34.103] [debug] cuFFT plan created: 1D plan: nx=64, type=41, batch=1
[09-12 17:22:34.103] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-12 17:22:34.103] [info] All FFT resource pools initialized successfully
[09-12 17:22:34.103] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-12 17:22:34.103] [info] Unified resource manager initialized successfully
[09-12 17:22:34.103] [info] 重新配置日志系统 - 路径: logs/runtime.log, 等级: debug
[09-12 17:22:34.103] [info] 日志系统初始化完成 - 文件等级: debug, 控制台等级: err
[09-12 17:22:34.103] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-12 17:22:34.118] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-12 17:22:34.118] [info] Engine info: 2 bindings
[09-12 17:22:34.118] [info]   Binding 0: input [1x1x1024x1024x2]
[09-12 17:22:34.118] [info]   Binding 1: output [1x1x512x1024]
[09-12 17:22:34.118] [info] 模型输入维度: [1x1024x1024x2]
[09-12 17:22:34.118] [debug] CUDA memory allocated: 8388608 bytes at 0x7fff6a600000
[09-12 17:22:34.118] [debug] CUDA memory allocated: 2097152 bytes at 0x7fff6ae00000
[09-12 17:22:34.118] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-12 17:22:34.123] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-12 17:22:34.123] [debug] - 显存 - 算法初始化完成: 使用 895.0 MB
[09-12 17:22:34.123] [debug] - 内存 - 算法初始化完成: 使用 259.1 MB
[09-12 17:22:34.123] [debug] =====算法库初始化完成=====
[09-12 17:22:34.232] [debug] =====开始目标检测=====
[09-12 17:22:34.232] [debug] 提取到 1024 个有效帧头
[09-12 17:22:34.325] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 17:40:40.623] [info] 日志系统初始化完成 - 文件等级: info, 控制台等级: err
[09-12 17:40:40.623] [info] Initializing unified resource manager...
[09-12 17:40:40.623] [info] Initializing GPU resource manager...
[09-12 17:40:40.701] [info] GPU resource manager initialized successfully
[09-12 17:40:40.701] [info] Initializing memory pool manager...
[09-12 17:40:40.701] [info] CUDA memory pool created with max size: 512MB
[09-12 17:40:40.701] [info] Host memory pool created with max size: 1024MB
[09-12 17:40:40.701] [info] Memory pool manager initialized successfully
[09-12 17:40:40.701] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-12 17:40:40.701] [info] Initializing FFT resource pools for 5 segment lengths
[09-12 17:40:40.703] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-12 17:40:40.703] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-12 17:40:40.704] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-12 17:40:40.704] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-12 17:40:40.704] [info] All FFT resource pools initialized successfully
[09-12 17:40:40.704] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-12 17:40:40.704] [info] Unified resource manager initialized successfully
[09-12 17:40:40.704] [info] 重新配置日志系统 - 路径: logs/runtime.log, 等级: info
[09-12 17:40:40.704] [info] 日志系统初始化完成 - 文件等级: info, 控制台等级: err
[09-12 17:40:40.704] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-12 17:40:40.755] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-12 17:40:40.756] [info] Engine info: 2 bindings
[09-12 17:40:40.756] [info]   Binding 0: input [1x1x1024x1024x2]
[09-12 17:40:40.756] [info]   Binding 1: output [1x1x512x1024]
[09-12 17:40:40.756] [info] 模型输入维度: [1x1024x1024x2]
[09-12 17:40:40.756] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-12 17:40:40.761] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-12 17:40:41.028] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 17:40:41.081] [warning] 无效帧:2458 模型没有检测到目标，跳过处理
[09-12 17:40:41.433] [warning] 无效帧:2459 模型没有检测到目标，跳过处理
[09-12 17:40:41.792] [warning] 无效帧:2460 模型没有检测到目标，跳过处理
[09-12 17:40:42.142] [warning] 无效帧:2461 模型没有检测到目标，跳过处理
[09-12 17:40:42.507] [info] Frame: 2462, Velo:   9.70, Range: 971.00, Amaz:  23.25, Elev:   7.20, Altitude: 121.70, X_cor: 1792, Y_cor: 565
[09-12 17:40:42.853] [info] Frame: 2463, Velo:   9.52, Range: 968.00, Amaz:  26.33, Elev:   5.80, Altitude:  97.82, X_cor: 1791, Y_cor: 564
[09-12 17:40:42.857] [info] Track[ 0] ID:  0 | Pos: ( 863.15,  427.10,   97.85) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.00 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:43.241] [warning] 无效帧:2464 模型没有检测到目标，跳过处理
[09-12 17:40:43.241] [info] Track[ 0] ID:  0 | Pos: ( 863.58,  427.31,   97.89) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.48 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:43.592] [info] 方位角变化，角度过滤，跳过帧: 2465，角度: 31.83，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:47.269] [info] Track[ 0] ID:  0 | Pos: ( 864.00,  427.52,   97.94) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.95 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:47.607] [info] 方位角变化，角度过滤，跳过帧: 2466，角度: 34.91，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:47.607] [info] Track[ 0] ID:  0 | Pos: ( 864.43,  427.73,   97.99) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.43 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:47.979] [info] 方位角变化，角度过滤，跳过帧: 2467，角度: 38.01，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:47.979] [info] Track[ 0] ID:  0 | Pos: ( 864.85,  427.94,   98.04) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.91 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:48.666] [info] 方位角变化，角度过滤，跳过帧: 2468，角度: 41.08，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:48.667] [info] Track[ 0] ID:  0 | Pos: ( 865.28,  428.15,   98.09) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.38 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:48.996] [info] 方位角变化，角度过滤，跳过帧: 2469，角度: 44.15，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:48.996] [info] Track[ 0] ID:  0 | Pos: ( 865.70,  428.36,   98.13) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.86 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:49.346] [info] 方位角变化，角度过滤，跳过帧: 2470，角度: 47.25，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:49.347] [info] Track[ 0] ID:  0 | Pos: ( 866.13,  428.57,   98.18) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.33 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:49.709] [info] 方位角变化，角度过滤，跳过帧: 2471，角度: 50.31，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:49.709] [info] Track[ 0] ID:  0 | Pos: ( 866.55,  428.78,   98.23) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.81 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:50.071] [info] 方位角变化，角度过滤，跳过帧: 2472，角度: 53.39，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:50.071] [info] Track[ 0] ID:  0 | Pos: ( 866.97,  428.99,   98.28) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.29 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:50.415] [info] 方位角变化，角度过滤，跳过帧: 2473，角度: 56.49，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:50.415] [info] Track[ 0] ID:  0 | Pos: ( 867.40,  429.20,   98.33) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.76 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:50.734] [warning] 无效帧:2572 模型没有检测到目标，跳过处理
[09-12 17:40:50.735] [info] Track[ 0] ID:  0 | Pos: ( 867.82,  429.41,   98.38) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.24 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:51.033] [warning] 无效帧:2573 模型没有检测到目标，跳过处理
[09-12 17:40:51.033] [info] Track[ 0] ID:  0 | Pos: ( 868.25,  429.62,   98.42) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.71 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:52.024] [warning] 无效帧:2574 模型没有检测到目标，跳过处理
[09-12 17:40:52.024] [info] Track[ 0] ID:  0 | Pos: ( 868.67,  429.84,   98.47) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.19 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:52.383] [warning] 无效帧:2575 模型没有检测到目标，跳过处理
[09-12 17:40:52.385] [info] Track[ 0] ID:  0 | Pos: ( 869.10,  430.05,   98.52) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.67 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:52.744] [warning] 无效帧:2576 模型没有检测到目标，跳过处理
[09-12 17:40:52.744] [info] Track[ 0] ID:  0 | Pos: ( 869.52,  430.26,   98.57) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.14 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:53.112] [warning] 无效帧:2577 模型没有检测到目标，跳过处理
[09-12 17:40:53.112] [info] Track[ 0] ID:  0 | Pos: ( 869.95,  430.47,   98.62) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.62 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:53.472] [warning] 无效帧:2578 模型没有检测到目标，跳过处理
[09-12 17:40:53.473] [info] Track[ 0] ID:  0 | Pos: ( 870.37,  430.68,   98.66) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 976.09 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:53.917] [info] Frame: 2579, Velo:   8.79, Range: 1028.00, Amaz:  22.34, Elev:   9.30, Altitude: 166.13, X_cor: 1811, Y_cor: 560
[09-12 17:40:53.918] [info] Track[ 0] ID:  0 | Pos: ( 870.79,  430.89,   98.71) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 976.57 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:40:54.347] [info] Frame: 2580, Velo:   8.79, Range: 1028.00, Amaz:  25.43, Elev:   6.20, Altitude: 111.02, X_cor: 1811, Y_cor: 560
[09-12 17:40:54.349] [info] Track[ 0] ID:  0 | Pos: ( 922.96,  438.85,  111.02) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1027.99 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:54.715] [warning] 无效帧:2581 模型没有检测到目标，跳过处理
[09-12 17:40:54.716] [info] Track[ 0] ID:  0 | Pos: ( 923.35,  439.04,  111.07) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1028.43 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:55.079] [info] 方位角变化，角度过滤，跳过帧: 2582，角度: 31.15，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:55.079] [info] Track[ 0] ID:  0 | Pos: ( 923.75,  439.22,  111.12) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1028.87 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:55.455] [info] 方位角变化，角度过滤，跳过帧: 2583，角度: 34.22，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:55.455] [info] Track[ 0] ID:  0 | Pos: ( 924.14,  439.41,  111.16) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1029.31 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:55.853] [info] 方位角变化，角度过滤，跳过帧: 2584，角度: 37.29，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:55.853] [info] Track[ 0] ID:  0 | Pos: ( 924.54,  439.60,  111.21) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1029.75 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:56.225] [info] 方位角变化，角度过滤，跳过帧: 2585，角度: 40.17，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:56.225] [info] Track[ 0] ID:  0 | Pos: ( 924.93,  439.79,  111.26) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1030.19 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:56.646] [info] 方位角变化，角度过滤，跳过帧: 2586，角度: 43.23，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:56.647] [info] Track[ 0] ID:  0 | Pos: ( 925.33,  439.97,  111.31) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1030.63 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:57.012] [info] 方位角变化，角度过滤，跳过帧: 2587，角度: 46.3，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:57.012] [info] Track[ 0] ID:  0 | Pos: ( 925.72,  440.16,  111.35) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1031.07 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:57.594] [info] 方位角变化，角度过滤，跳过帧: 2588，角度: 49.4，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:57.595] [info] Track[ 0] ID:  0 | Pos: ( 926.11,  440.35,  111.40) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1031.51 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:57.955] [info] 方位角变化，角度过滤，跳过帧: 2589，角度: 52.47，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:57.955] [info] Track[ 0] ID:  0 | Pos: ( 926.51,  440.54,  111.45) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1031.95 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:58.384] [info] 方位角变化，角度过滤，跳过帧: 2590，角度: 55.54，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:58.384] [info] Track[ 0] ID:  0 | Pos: ( 926.90,  440.72,  111.50) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1032.39 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:58.753] [info] 方位角变化，角度过滤，跳过帧: 2591，角度: 58.62，目标数量: 0, 列数据段数量: 0 
[09-12 17:40:58.754] [info] Track[ 0] ID:  0 | Pos: ( 927.30,  440.91,  111.54) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1032.82 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:59.103] [warning] 无效帧:2690 模型没有检测到目标，跳过处理
[09-12 17:40:59.103] [info] Track[ 0] ID:  0 | Pos: ( 927.69,  441.10,  111.59) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1033.26 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:59.462] [warning] 无效帧:2691 模型没有检测到目标，跳过处理
[09-12 17:40:59.463] [info] Track[ 0] ID:  0 | Pos: ( 928.09,  441.29,  111.64) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1033.70 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:40:59.816] [warning] 无效帧:2692 模型没有检测到目标，跳过处理
[09-12 17:40:59.817] [info] Track[ 0] ID:  0 | Pos: ( 928.48,  441.47,  111.69) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1034.14 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:00.173] [warning] 无效帧:2693 模型没有检测到目标，跳过处理
[09-12 17:41:00.174] [info] Track[ 0] ID:  0 | Pos: ( 928.88,  441.66,  111.73) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1034.58 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:00.530] [warning] 无效帧:2694 模型没有检测到目标，跳过处理
[09-12 17:41:00.533] [info] Track[ 0] ID:  0 | Pos: ( 929.27,  441.85,  111.78) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1035.02 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:00.879] [warning] 无效帧:2695 模型没有检测到目标，跳过处理
[09-12 17:41:00.879] [info] Track[ 0] ID:  0 | Pos: ( 929.67,  442.04,  111.83) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1035.46 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:01.711] [info] Frame: 2696, Velo:   8.97, Range: 1079.00, Amaz:  21.65, Elev:   7.60, Altitude: 142.70, X_cor: 1828, Y_cor: 561
[09-12 17:41:01.713] [info] Track[ 0] ID:  0 | Pos: ( 930.06,  442.22,  111.88) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1035.90 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:01.713] [info] Track[ 1] ID:  1 | Pos: ( 994.07,  394.58,  142.71) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1079.00 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:02.057] [info] Frame: 2697, Velo:   8.79, Range: 1079.00, Amaz:  24.74, Elev:   6.50, Altitude: 122.15, X_cor: 1828, Y_cor: 560
[09-12 17:41:02.058] [info] Track[ 0] ID:  0 | Pos: ( 973.66,  448.66,  122.14) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1078.99 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:02.058] [info] Track[ 1] ID:  1 | Pos: ( 994.48,  394.75,  142.76) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1079.45 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:02.737] [warning] 无效帧:2698 模型没有检测到目标，跳过处理
[09-12 17:41:02.737] [info] Track[ 0] ID:  0 | Pos: ( 974.05,  448.84,  122.19) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1079.43 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:02.737] [info] Track[ 1] ID:  1 | Pos: ( 994.90,  394.91,  142.82) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1079.90 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:03.198] [info] 方位角变化，角度过滤，跳过帧: 2699，角度: 30.21，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:03.199] [info] Track[ 0] ID:  0 | Pos: ( 974.45,  449.02,  122.24) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1079.87 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:03.199] [info] Track[ 1] ID:  1 | Pos: ( 995.31,  395.08,  142.88) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1080.34 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:03.555] [info] 方位角变化，角度过滤，跳过帧: 2700，角度: 33.29，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:03.555] [info] Track[ 0] ID:  0 | Pos: ( 974.85,  449.21,  122.29) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1080.31 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:03.555] [info] Track[ 1] ID:  1 | Pos: ( 995.72,  395.24,  142.94) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1080.79 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:03.895] [info] 方位角变化，角度过滤，跳过帧: 2701，角度: 36.37，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:03.895] [info] Track[ 0] ID:  0 | Pos: ( 975.24,  449.39,  122.34) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1080.75 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:03.895] [info] Track[ 1] ID:  1 | Pos: ( 996.14,  395.40,  143.00) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1081.24 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:04.264] [info] 方位角变化，角度过滤，跳过帧: 2702，角度: 39.45，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:04.264] [info] Track[ 0] ID:  0 | Pos: ( 975.64,  449.57,  122.39) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1081.19 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:04.264] [info] Track[ 1] ID:  1 | Pos: ( 996.55,  395.57,  143.06) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1081.69 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:04.614] [info] 方位角变化，角度过滤，跳过帧: 2703，角度: 42.53，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:04.614] [info] Track[ 0] ID:  0 | Pos: ( 976.04,  449.76,  122.44) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1081.63 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:04.614] [info] Track[ 1] ID:  1 | Pos: ( 996.96,  395.73,  143.12) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1082.14 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:05.268] [info] 方位角变化，角度过滤，跳过帧: 2704，角度: 45.61，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:05.268] [info] Track[ 0] ID:  0 | Pos: ( 976.43,  449.94,  122.49) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1082.07 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:05.268] [info] Track[ 1] ID:  1 | Pos: ( 997.38,  395.90,  143.18) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1082.59 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:05.623] [info] 方位角变化，角度过滤，跳过帧: 2705，角度: 48.68，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:05.623] [info] Track[ 0] ID:  0 | Pos: ( 976.83,  450.12,  122.54) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1082.51 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:05.623] [info] Track[ 1] ID:  1 | Pos: ( 997.79,  396.06,  143.24) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1083.04 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:06.233] [info] 方位角变化，角度过滤，跳过帧: 2706，角度: 51.76，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:06.233] [info] Track[ 0] ID:  0 | Pos: ( 977.23,  450.30,  122.59) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1082.95 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:06.233] [info] Track[ 1] ID:  1 | Pos: ( 998.20,  396.23,  143.30) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1083.48 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:06.576] [info] 方位角变化，角度过滤，跳过帧: 2707，角度: 54.86，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:06.576] [info] Track[ 0] ID:  0 | Pos: ( 977.62,  450.49,  122.64) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1083.39 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:06.577] [info] Track[ 1] ID:  1 | Pos: ( 998.62,  396.39,  143.36) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1083.93 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:06.931] [info] 方位角变化，角度过滤，跳过帧: 2708，角度: 57.93，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:06.931] [info] Track[ 0] ID:  0 | Pos: ( 978.02,  450.67,  122.69) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1083.83 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:06.932] [info] Track[ 1] ID:  1 | Pos: ( 999.03,  396.55,  143.42) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1084.38 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:07.290] [warning] 无效帧:2807 模型没有检测到目标，跳过处理
[09-12 17:41:07.290] [info] Track[ 0] ID:  0 | Pos: ( 978.42,  450.85,  122.74) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1084.27 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:07.290] [info] Track[ 1] ID:  1 | Pos: ( 999.44,  396.72,  143.48) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1084.83 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:07.669] [warning] 无效帧:2808 模型没有检测到目标，跳过处理
[09-12 17:41:07.670] [info] Track[ 0] ID:  0 | Pos: ( 978.81,  451.03,  122.79) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1084.71 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:07.670] [info] Track[ 1] ID:  1 | Pos: ( 999.86,  396.88,  143.54) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1085.28 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:08.052] [warning] 无效帧:2809 模型没有检测到目标，跳过处理
[09-12 17:41:08.053] [info] Track[ 0] ID:  0 | Pos: ( 979.21,  451.22,  122.84) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1085.14 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:08.053] [info] Track[ 1] ID:  1 | Pos: (1000.27,  397.05,  143.60) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1085.73 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:08.406] [warning] 无效帧:2810 模型没有检测到目标，跳过处理
[09-12 17:41:08.406] [info] Track[ 0] ID:  0 | Pos: ( 979.61,  451.40,  122.89) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1085.58 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:08.406] [info] Track[ 1] ID:  1 | Pos: (1000.68,  397.21,  143.65) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1086.18 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:08.748] [warning] 无效帧:2811 模型没有检测到目标，跳过处理
[09-12 17:41:08.749] [info] Track[ 0] ID:  0 | Pos: ( 980.00,  451.58,  122.94) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1086.02 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:08.749] [info] Track[ 1] ID:  1 | Pos: (1001.10,  397.37,  143.71) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1086.62 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:09.104] [warning] 无效帧:2812 模型没有检测到目标，跳过处理
[09-12 17:41:09.104] [info] Track[ 0] ID:  0 | Pos: ( 980.40,  451.77,  122.99) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1086.46 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:09.104] [info] Track[ 1] ID:  1 | Pos: (1001.51,  397.54,  143.77) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1087.07 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:09.463] [info] Frame: 2813, Velo:   8.97, Range: 1151.00, Amaz:  20.94, Elev:   5.30, Altitude: 106.32, X_cor: 1852, Y_cor: 561
[09-12 17:41:09.465] [info] Track[ 0] ID:  0 | Pos: ( 980.80,  451.95,  123.04) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1086.90 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:09.465] [info] Track[ 1] ID:  1 | Pos: (1070.37,  409.60,  106.33) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1150.99 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:09.853] [info] Frame: 2814, Velo:   8.79, Range: 1136.00, Amaz:  23.80, Elev:   5.50, Altitude: 108.88, X_cor: 1847, Y_cor: 560
[09-12 17:41:09.855] [info] Track[ 0] ID:  0 | Pos: (1034.60,  456.32,  108.88) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1135.99 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:09.855] [info] Track[ 1] ID:  1 | Pos: (1070.79,  409.75,  106.37) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1151.44 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:10.414] [warning] 无效帧:2815 模型没有检测到目标，跳过处理
[09-12 17:41:10.418] [info] Track[ 0] ID:  0 | Pos: (1035.00,  456.49,  108.93) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1136.43 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:10.418] [info] Track[ 1] ID:  1 | Pos: (1071.21,  409.91,  106.41) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1151.89 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:10.782] [warning] 无效帧:2816 模型没有检测到目标，跳过处理
[09-12 17:41:10.783] [info] Track[ 0] ID:  0 | Pos: (1035.40,  456.67,  108.97) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1136.87 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:10.783] [info] Track[ 1] ID:  1 | Pos: (1071.63,  410.07,  106.45) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1152.33 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:11.501] [info] 方位角变化，角度过滤，跳过帧: 2817，角度: 32.6，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:11.502] [info] Track[ 0] ID:  0 | Pos: (1035.80,  456.85,  109.01) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1137.31 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:11.502] [info] Track[ 1] ID:  1 | Pos: (1072.04,  410.23,  106.49) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1152.78 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:11.854] [info] 方位角变化，角度过滤，跳过帧: 2818，角度: 35.67，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:11.855] [info] Track[ 0] ID:  0 | Pos: (1036.20,  457.02,  109.05) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1137.75 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:11.855] [info] Track[ 1] ID:  1 | Pos: (1072.46,  410.39,  106.53) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1153.23 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:12.220] [info] 方位角变化，角度过滤，跳过帧: 2819，角度: 38.76，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:12.220] [info] Track[ 0] ID:  0 | Pos: (1036.60,  457.20,  109.09) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1138.19 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:12.220] [info] Track[ 1] ID:  1 | Pos: (1072.88,  410.55,  106.57) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1153.68 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:12.570] [info] 方位角变化，角度过滤，跳过帧: 2820，角度: 41.61，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:12.570] [info] Track[ 0] ID:  0 | Pos: (1037.00,  457.38,  109.14) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1138.63 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:12.570] [info] Track[ 1] ID:  1 | Pos: (1073.29,  410.71,  106.62) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1154.13 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:12.932] [info] 方位角变化，角度过滤，跳过帧: 2821，角度: 44.68，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:12.932] [info] Track[ 0] ID:  0 | Pos: (1037.40,  457.55,  109.18) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1139.07 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:12.932] [info] Track[ 1] ID:  1 | Pos: (1073.71,  410.87,  106.66) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1154.58 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:13.280] [info] 方位角变化，角度过滤，跳过帧: 2822，角度: 47.79，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:13.280] [info] Track[ 0] ID:  0 | Pos: (1037.80,  457.73,  109.22) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1139.51 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:13.280] [info] Track[ 1] ID:  1 | Pos: (1074.13,  411.03,  106.70) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1155.03 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:13.629] [info] 方位角变化，角度过滤，跳过帧: 2823，角度: 50.85，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:13.629] [info] Track[ 0] ID:  0 | Pos: (1038.20,  457.90,  109.26) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1139.95 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:13.629] [info] Track[ 1] ID:  1 | Pos: (1074.55,  411.19,  106.74) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1155.47 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:13.981] [info] 方位角变化，角度过滤，跳过帧: 2824，角度: 53.92，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:13.982] [info] Track[ 0] ID:  0 | Pos: (1038.60,  458.08,  109.30) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1140.39 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:13.982] [info] Track[ 1] ID:  1 | Pos: (1074.96,  411.35,  106.78) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1155.92 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:14.101] [warning] 无效帧:2455 模型没有检测到目标，跳过处理
[09-12 17:41:14.101] [info] Track[ 0] ID:  0 | Pos: (1039.00,  458.26,  109.35) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1140.83 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:14.101] [info] Track[ 1] ID:  1 | Pos: (1075.38,  411.51,  106.82) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1156.37 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:14.222] [warning] 无效帧:2456 模型没有检测到目标，跳过处理
[09-12 17:41:14.222] [info] Track[ 0] ID:  0 | Pos: (1039.40,  458.43,  109.39) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1141.26 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:14.222] [info] Track[ 1] ID:  1 | Pos: (1075.80,  411.67,  106.86) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1156.82 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:14.334] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 17:41:14.335] [info] Track[ 0] ID:  0 | Pos: (1039.80,  458.61,  109.43) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1141.70 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:14.335] [info] Track[ 1] ID:  1 | Pos: (1076.22,  411.83,  106.91) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1157.27 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:14.454] [warning] 无效帧:2458 模型没有检测到目标，跳过处理
[09-12 17:41:14.454] [info] Track[ 0] ID:  0 | Pos: (1040.20,  458.79,  109.47) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1142.14 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:14.454] [info] Track[ 1] ID:  1 | Pos: (1076.63,  411.99,  106.95) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1157.72 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:14.567] [warning] 无效帧:2459 模型没有检测到目标，跳过处理
[09-12 17:41:14.569] [info] Track[ 0] ID:  0 | Pos: (1040.60,  458.96,  109.51) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1142.58 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:14.569] [info] Track[ 1] ID:  1 | Pos: (1077.05,  412.15,  106.99) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1158.17 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:14.678] [warning] 无效帧:2460 模型没有检测到目标，跳过处理
[09-12 17:41:14.679] [info] Track[ 0] ID:  0 | Pos: (1041.00,  459.14,  109.56) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1143.02 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:14.679] [info] Track[ 1] ID:  1 | Pos: (1077.47,  412.31,  107.03) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1158.62 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:14.798] [warning] 无效帧:2461 模型没有检测到目标，跳过处理
[09-12 17:41:14.798] [info] Track[ 0] ID:  0 | Pos: (1041.40,  459.32,  109.60) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1143.46 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:14.798] [info] Track[ 1] ID:  1 | Pos: (1077.88,  412.47,  107.07) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1159.06 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:14.926] [info] Frame: 2462, Velo:   9.70, Range: 971.00, Amaz:  23.25, Elev:   7.20, Altitude: 121.70, X_cor: 1792, Y_cor: 565
[09-12 17:41:14.928] [info] Track[ 0] ID:  0 | Pos: (1041.80,  459.49,  109.64) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1143.90 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:14.928] [info] Track[ 1] ID:  1 | Pos: (1078.30,  412.63,  107.11) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1159.51 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.037] [info] Frame: 2463, Velo:   9.52, Range: 968.00, Amaz:  26.33, Elev:   5.80, Altitude:  97.82, X_cor: 1791, Y_cor: 564
[09-12 17:41:15.039] [info] Track[ 0] ID:  0 | Pos: (1042.20,  459.67,  109.68) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1144.34 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:15.039] [info] Track[ 1] ID:  1 | Pos: (1078.72,  412.79,  107.15) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1159.96 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.039] [info] Track[ 2] ID:  2 | Pos: ( 863.15,  427.10,   97.85) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.00 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:15.148] [warning] 无效帧:2464 模型没有检测到目标，跳过处理
[09-12 17:41:15.152] [info] Track[ 0] ID:  0 | Pos: (1042.60,  459.85,  109.73) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1144.78 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:15.152] [info] Track[ 1] ID:  1 | Pos: (1079.14,  412.95,  107.20) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1160.41 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.152] [info] Track[ 2] ID:  2 | Pos: ( 863.58,  427.31,   97.89) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.48 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:15.250] [info] 方位角变化，角度过滤，跳过帧: 2465，角度: 31.83，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:15.251] [info] Track[ 0] ID:  0 | Pos: (1043.00,  460.02,  109.77) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1145.22 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:15.251] [info] Track[ 1] ID:  1 | Pos: (1079.55,  413.11,  107.24) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1160.86 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.251] [info] Track[ 2] ID:  2 | Pos: ( 864.00,  427.52,   97.94) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.95 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:15.357] [info] 方位角变化，角度过滤，跳过帧: 2466，角度: 34.91，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:15.358] [info] Track[ 0] ID:  0 | Pos: (1043.41,  460.20,  109.81) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1145.66 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:15.358] [info] Track[ 1] ID:  1 | Pos: (1079.97,  413.27,  107.28) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1161.31 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.358] [info] Track[ 2] ID:  2 | Pos: ( 864.43,  427.73,   97.99) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.43 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:15.467] [info] 方位角变化，角度过滤，跳过帧: 2467，角度: 38.01，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:15.467] [info] Track[ 0] ID:  0 | Pos: (1043.81,  460.38,  109.85) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1146.10 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:15.467] [info] Track[ 1] ID:  1 | Pos: (1080.39,  413.43,  107.32) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1161.76 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.467] [info] Track[ 2] ID:  2 | Pos: ( 864.85,  427.94,   98.04) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.91 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:15.584] [info] 方位角变化，角度过滤，跳过帧: 2468，角度: 41.08，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:15.584] [info] Track[ 0] ID:  0 | Pos: (1044.21,  460.55,  109.89) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1146.54 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:15.584] [info] Track[ 1] ID:  1 | Pos: (1080.80,  413.59,  107.36) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1162.20 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.584] [info] Track[ 2] ID:  2 | Pos: ( 865.28,  428.15,   98.09) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.38 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:15.691] [info] 方位角变化，角度过滤，跳过帧: 2469，角度: 44.15，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:15.691] [info] Track[ 0] ID:  0 | Pos: (1044.61,  460.73,  109.94) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1146.98 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:15.691] [info] Track[ 1] ID:  1 | Pos: (1081.22,  413.75,  107.40) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1162.65 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.691] [info] Track[ 2] ID:  2 | Pos: ( 865.70,  428.36,   98.13) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.86 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:15.799] [info] 方位角变化，角度过滤，跳过帧: 2470，角度: 47.25，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:15.800] [info] Track[ 0] ID:  0 | Pos: (1045.01,  460.91,  109.98) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1147.42 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:15.800] [info] Track[ 1] ID:  1 | Pos: (1081.64,  413.91,  107.44) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1163.10 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.800] [info] Track[ 2] ID:  2 | Pos: ( 866.13,  428.57,   98.18) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.33 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:15.907] [info] 方位角变化，角度过滤，跳过帧: 2471，角度: 50.31，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:15.908] [info] Track[ 0] ID:  0 | Pos: (1045.41,  461.08,  110.02) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1147.86 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:15.908] [info] Track[ 1] ID:  1 | Pos: (1082.06,  414.07,  107.48) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1163.55 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:15.908] [info] Track[ 2] ID:  2 | Pos: ( 866.55,  428.78,   98.23) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.81 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:16.019] [info] 方位角变化，角度过滤，跳过帧: 2472，角度: 53.39，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:16.019] [info] Track[ 0] ID:  0 | Pos: (1045.81,  461.26,  110.06) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1148.30 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:16.019] [info] Track[ 1] ID:  1 | Pos: (1082.47,  414.22,  107.53) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1164.00 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:16.019] [info] Track[ 2] ID:  2 | Pos: ( 866.97,  428.99,   98.28) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.29 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:16.128] [info] 方位角变化，角度过滤，跳过帧: 2473，角度: 56.49，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:16.128] [info] Track[ 0] ID:  0 | Pos: (1046.21,  461.44,  110.10) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1148.74 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:16.128] [info] Track[ 1] ID:  1 | Pos: (1082.89,  414.38,  107.57) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1164.45 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:16.128] [info] Track[ 2] ID:  2 | Pos: ( 867.40,  429.20,   98.33) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.76 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:16.244] [warning] 无效帧:2572 模型没有检测到目标，跳过处理
[09-12 17:41:16.244] [info] Track[ 0] ID:  0 | Pos: (1046.61,  461.61,  110.15) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1149.18 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:16.244] [info] Track[ 1] ID:  1 | Pos: (1083.31,  414.54,  107.61) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1164.90 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:16.244] [info] Track[ 2] ID:  2 | Pos: ( 867.82,  429.41,   98.38) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.24 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:16.356] [warning] 无效帧:2573 模型没有检测到目标，跳过处理
[09-12 17:41:16.357] [info] Track[ 0] ID:  0 | Pos: (1047.01,  461.79,  110.19) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1149.61 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:16.357] [info] Track[ 1] ID:  1 | Pos: (1083.73,  414.70,  107.65) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1165.34 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:16.357] [info] Track[ 2] ID:  2 | Pos: ( 868.25,  429.62,   98.42) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.71 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:16.469] [warning] 无效帧:2574 模型没有检测到目标，跳过处理
[09-12 17:41:16.469] [info] Track[ 0] ID:  0 | Pos: (1047.41,  461.96,  110.23) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1150.05 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:16.469] [info] Track[ 1] ID:  1 | Pos: (1084.14,  414.86,  107.69) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1165.79 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:16.469] [info] Track[ 2] ID:  2 | Pos: ( 868.67,  429.84,   98.47) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.19 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:16.583] [warning] 无效帧:2575 模型没有检测到目标，跳过处理
[09-12 17:41:16.584] [info] Track[ 0] ID:  0 | Pos: (1047.81,  462.14,  110.27) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1150.49 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:16.584] [info] Track[ 1] ID:  1 | Pos: (1084.56,  415.02,  107.73) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1166.24 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:16.584] [info] Track[ 2] ID:  2 | Pos: ( 869.10,  430.05,   98.52) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.67 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:16.700] [warning] 无效帧:2576 模型没有检测到目标，跳过处理
[09-12 17:41:16.702] [info] Track[ 0] ID:  0 | Pos: (1048.21,  462.32,  110.31) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1150.93 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:16.702] [info] Track[ 1] ID:  1 | Pos: (1084.98,  415.18,  107.77) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1166.69 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:16.702] [info] Track[ 2] ID:  2 | Pos: ( 869.52,  430.26,   98.57) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.14 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:16.813] [warning] 无效帧:2577 模型没有检测到目标，跳过处理
[09-12 17:41:16.813] [info] Track[ 0] ID:  0 | Pos: (1048.61,  462.49,  110.36) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1151.37 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:16.813] [info] Track[ 1] ID:  1 | Pos: (1085.39,  415.34,  107.82) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1167.14 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:16.813] [info] Track[ 2] ID:  2 | Pos: ( 869.95,  430.47,   98.62) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.62 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:16.923] [warning] 无效帧:2578 模型没有检测到目标，跳过处理
[09-12 17:41:16.925] [info] Track[ 0] ID:  0 | Pos: (1049.01,  462.67,  110.40) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1151.81 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:16.925] [info] Track[ 1] ID:  1 | Pos: (1085.81,  415.50,  107.86) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1167.59 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:16.925] [info] Track[ 2] ID:  2 | Pos: ( 870.37,  430.68,   98.66) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 976.09 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:17.036] [info] Frame: 2579, Velo:   8.79, Range: 1028.00, Amaz:  22.34, Elev:   9.30, Altitude: 166.13, X_cor: 1811, Y_cor: 560
[09-12 17:41:17.038] [info] Track[ 0] ID:  0 | Pos: (1049.41,  462.85,  110.44) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1152.25 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:17.038] [info] Track[ 1] ID:  1 | Pos: (1086.23,  415.66,  107.90) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1168.04 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:17.038] [info] Track[ 2] ID:  2 | Pos: ( 870.79,  430.89,   98.71) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 976.57 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:17.152] [info] Frame: 2580, Velo:   8.79, Range: 1028.00, Amaz:  25.43, Elev:   6.20, Altitude: 111.02, X_cor: 1811, Y_cor: 560
[09-12 17:41:17.154] [info] Track[ 0] ID:  0 | Pos: (1049.81,  463.02,  110.48) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1152.69 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:17.154] [info] Track[ 1] ID:  1 | Pos: (1086.65,  415.82,  107.94) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1168.49 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:17.154] [info] Track[ 2] ID:  2 | Pos: ( 922.96,  438.85,  111.02) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1027.99 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:17.267] [warning] 无效帧:2581 模型没有检测到目标，跳过处理
[09-12 17:41:17.268] [info] Track[ 0] ID:  0 | Pos: (1050.21,  463.20,  110.53) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1153.13 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:17.268] [info] Track[ 1] ID:  1 | Pos: (1087.06,  415.98,  107.98) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1168.93 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:17.268] [info] Track[ 2] ID:  2 | Pos: ( 923.35,  439.04,  111.07) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1028.43 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:17.366] [info] 方位角变化，角度过滤，跳过帧: 2582，角度: 31.15，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:17.366] [info] Track[ 0] ID:  0 | Pos: (1050.61,  463.38,  110.57) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1153.57 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:17.366] [info] Track[ 1] ID:  1 | Pos: (1087.48,  416.14,  108.02) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1169.38 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:17.366] [info] Track[ 2] ID:  2 | Pos: ( 923.75,  439.22,  111.12) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1028.87 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:17.475] [info] 方位角变化，角度过滤，跳过帧: 2583，角度: 34.22，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:17.475] [info] Track[ 0] ID:  0 | Pos: (1051.01,  463.55,  110.61) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1154.01 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:17.475] [info] Track[ 1] ID:  1 | Pos: (1087.90,  416.30,  108.06) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1169.83 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:17.475] [info] Track[ 2] ID:  2 | Pos: ( 924.14,  439.41,  111.16) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1029.31 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:17.583] [info] 方位角变化，角度过滤，跳过帧: 2584，角度: 37.29，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:17.583] [info] Track[ 0] ID:  0 | Pos: (1051.41,  463.73,  110.65) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1154.45 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:17.583] [info] Track[ 1] ID:  1 | Pos: (1088.32,  416.46,  108.11) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1170.28 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:17.583] [info] Track[ 2] ID:  2 | Pos: ( 924.54,  439.60,  111.21) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1029.75 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:17.692] [info] 方位角变化，角度过滤，跳过帧: 2585，角度: 40.17，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:17.693] [info] Track[ 0] ID:  0 | Pos: (1051.81,  463.91,  110.69) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1154.89 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:17.693] [info] Track[ 1] ID:  1 | Pos: (1088.73,  416.62,  108.15) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1170.73 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:17.693] [info] Track[ 2] ID:  2 | Pos: ( 924.93,  439.79,  111.26) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1030.19 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:17.801] [info] 方位角变化，角度过滤，跳过帧: 2586，角度: 43.23，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:17.801] [info] Track[ 0] ID:  0 | Pos: (1052.21,  464.08,  110.74) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1155.33 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:17.801] [info] Track[ 1] ID:  1 | Pos: (1089.15,  416.78,  108.19) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1171.18 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:17.802] [info] Track[ 2] ID:  2 | Pos: ( 925.33,  439.97,  111.31) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1030.63 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:17.909] [info] 方位角变化，角度过滤，跳过帧: 2587，角度: 46.3，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:17.910] [info] Track[ 0] ID:  0 | Pos: (1052.61,  464.26,  110.78) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1155.77 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:17.910] [info] Track[ 1] ID:  1 | Pos: (1089.57,  416.94,  108.23) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1171.63 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:17.910] [info] Track[ 2] ID:  2 | Pos: ( 925.72,  440.16,  111.35) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1031.07 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:18.018] [info] 方位角变化，角度过滤，跳过帧: 2588，角度: 49.4，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:18.018] [info] Track[ 0] ID:  0 | Pos: (1053.01,  464.44,  110.82) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1156.21 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:18.018] [info] Track[ 1] ID:  1 | Pos: (1089.98,  417.10,  108.27) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1172.07 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:18.018] [info] Track[ 2] ID:  2 | Pos: ( 926.11,  440.35,  111.40) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1031.51 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:18.126] [info] 方位角变化，角度过滤，跳过帧: 2589，角度: 52.47，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:18.126] [info] Track[ 0] ID:  0 | Pos: (1053.41,  464.61,  110.86) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1156.65 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:18.126] [info] Track[ 1] ID:  1 | Pos: (1090.40,  417.26,  108.31) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1172.52 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:18.127] [info] Track[ 2] ID:  2 | Pos: ( 926.51,  440.54,  111.45) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1031.95 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:18.248] [info] 方位角变化，角度过滤，跳过帧: 2590，角度: 55.54，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:18.249] [info] Track[ 0] ID:  0 | Pos: (1053.81,  464.79,  110.90) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1157.09 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:18.249] [info] Track[ 1] ID:  1 | Pos: (1090.82,  417.42,  108.35) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1172.97 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:18.249] [info] Track[ 2] ID:  2 | Pos: ( 926.90,  440.72,  111.50) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1032.39 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:18.360] [info] 方位角变化，角度过滤，跳过帧: 2591，角度: 58.62，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:18.360] [info] Track[ 0] ID:  0 | Pos: (1054.21,  464.97,  110.95) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1157.53 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:18.360] [info] Track[ 1] ID:  1 | Pos: (1091.24,  417.58,  108.40) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1173.42 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:18.360] [info] Track[ 2] ID:  2 | Pos: ( 927.30,  440.91,  111.54) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1032.82 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:18.475] [warning] 无效帧:2690 模型没有检测到目标，跳过处理
[09-12 17:41:18.477] [info] Track[ 0] ID:  0 | Pos: (1054.61,  465.14,  110.99) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1157.97 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:18.477] [info] Track[ 1] ID:  1 | Pos: (1091.65,  417.74,  108.44) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1173.87 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:18.477] [info] Track[ 2] ID:  2 | Pos: ( 927.69,  441.10,  111.59) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1033.26 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:18.587] [warning] 无效帧:2691 模型没有检测到目标，跳过处理
[09-12 17:41:18.587] [info] Track[ 0] ID:  0 | Pos: (1055.01,  465.32,  111.03) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1158.40 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:18.588] [info] Track[ 1] ID:  1 | Pos: (1092.07,  417.90,  108.48) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1174.32 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:18.588] [info] Track[ 2] ID:  2 | Pos: ( 928.09,  441.29,  111.64) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1033.70 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:18.698] [warning] 无效帧:2692 模型没有检测到目标，跳过处理
[09-12 17:41:18.699] [info] Track[ 0] ID:  0 | Pos: (1055.41,  465.49,  111.07) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1158.84 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:18.699] [info] Track[ 1] ID:  1 | Pos: (1092.49,  418.06,  108.52) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1174.77 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:18.699] [info] Track[ 2] ID:  2 | Pos: ( 928.48,  441.47,  111.69) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1034.14 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:18.810] [warning] 无效帧:2693 模型没有检测到目标，跳过处理
[09-12 17:41:18.811] [info] Track[ 0] ID:  0 | Pos: (1055.81,  465.67,  111.12) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1159.28 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:18.811] [info] Track[ 1] ID:  1 | Pos: (1092.90,  418.22,  108.56) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1175.22 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:18.811] [info] Track[ 2] ID:  2 | Pos: ( 928.88,  441.66,  111.73) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1034.58 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:18.916] [warning] 无效帧:2694 模型没有检测到目标，跳过处理
[09-12 17:41:18.918] [info] Track[ 0] ID:  0 | Pos: (1056.21,  465.85,  111.16) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1159.72 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:18.918] [info] Track[ 1] ID:  1 | Pos: (1093.32,  418.38,  108.60) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1175.66 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:18.918] [info] Track[ 2] ID:  2 | Pos: ( 929.27,  441.85,  111.78) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1035.02 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:19.032] [warning] 无效帧:2695 模型没有检测到目标，跳过处理
[09-12 17:41:19.035] [info] Track[ 0] ID:  0 | Pos: (1056.61,  466.02,  111.20) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1160.16 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:19.035] [info] Track[ 1] ID:  1 | Pos: (1093.74,  418.53,  108.64) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1176.11 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:19.035] [info] Track[ 2] ID:  2 | Pos: ( 929.67,  442.04,  111.83) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1035.46 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:19.138] [info] Frame: 2696, Velo:   8.97, Range: 1079.00, Amaz:  21.65, Elev:   7.60, Altitude: 142.70, X_cor: 1828, Y_cor: 561
[09-12 17:41:19.141] [info] Track[ 0] ID:  0 | Pos: (1057.01,  466.20,  111.24) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1160.60 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:19.141] [info] Track[ 1] ID:  1 | Pos: (1094.16,  418.69,  108.69) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1176.56 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:19.141] [info] Track[ 2] ID:  2 | Pos: ( 930.06,  442.22,  111.88) m | Vel: (  7.89,   3.75,   0.95) m/s | R: 1035.90 m | Vr:   8.79 m/s | Az:  25.43° | El:   6.20°
[09-12 17:41:19.141] [info] Track[ 3] ID:  3 | Pos: ( 994.07,  394.58,  142.71) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1079.00 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:19.252] [info] Frame: 2697, Velo:   8.79, Range: 1079.00, Amaz:  24.74, Elev:   6.50, Altitude: 122.15, X_cor: 1828, Y_cor: 560
[09-12 17:41:19.253] [info] Track[ 0] ID:  0 | Pos: (1057.41,  466.38,  111.28) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1161.04 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:19.253] [info] Track[ 1] ID:  1 | Pos: (1094.57,  418.85,  108.73) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1177.01 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:19.253] [info] Track[ 2] ID:  2 | Pos: ( 973.66,  448.66,  122.14) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1078.99 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:19.253] [info] Track[ 3] ID:  3 | Pos: ( 994.48,  394.75,  142.76) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1079.45 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:19.365] [warning] 无效帧:2698 模型没有检测到目标，跳过处理
[09-12 17:41:19.365] [info] Track[ 0] ID:  0 | Pos: (1057.81,  466.55,  111.33) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1161.48 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:19.366] [info] Track[ 1] ID:  1 | Pos: (1094.99,  419.01,  108.77) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1177.46 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:19.366] [info] Track[ 2] ID:  2 | Pos: ( 974.05,  448.84,  122.19) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1079.43 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:19.366] [info] Track[ 3] ID:  3 | Pos: ( 994.90,  394.91,  142.82) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1079.90 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:19.466] [info] 方位角变化，角度过滤，跳过帧: 2699，角度: 30.21，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:19.467] [info] Track[ 0] ID:  0 | Pos: (1058.21,  466.73,  111.37) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1161.92 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:19.467] [info] Track[ 1] ID:  1 | Pos: (1095.41,  419.17,  108.81) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1177.91 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:19.467] [info] Track[ 2] ID:  2 | Pos: ( 974.45,  449.02,  122.24) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1079.87 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:19.467] [info] Track[ 3] ID:  3 | Pos: ( 995.31,  395.08,  142.88) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1080.34 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:19.581] [info] 方位角变化，角度过滤，跳过帧: 2700，角度: 33.29，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:19.581] [info] Track[ 0] ID:  0 | Pos: (1058.62,  466.91,  111.41) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1162.36 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:19.581] [info] Track[ 1] ID:  2 | Pos: ( 974.85,  449.21,  122.29) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1080.31 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:19.581] [info] Track[ 2] ID:  3 | Pos: ( 995.72,  395.24,  142.94) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1080.79 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:19.689] [info] 方位角变化，角度过滤，跳过帧: 2701，角度: 36.37，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:19.689] [info] Track[ 0] ID:  2 | Pos: ( 975.24,  449.39,  122.34) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1080.75 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:19.689] [info] Track[ 1] ID:  3 | Pos: ( 996.14,  395.40,  143.00) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1081.24 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:19.797] [info] 方位角变化，角度过滤，跳过帧: 2702，角度: 39.45，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:19.798] [info] Track[ 0] ID:  2 | Pos: ( 975.64,  449.57,  122.39) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1081.19 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:19.798] [info] Track[ 1] ID:  3 | Pos: ( 996.55,  395.57,  143.06) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1081.69 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:19.907] [info] 方位角变化，角度过滤，跳过帧: 2703，角度: 42.53，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:19.908] [info] Track[ 0] ID:  2 | Pos: ( 976.04,  449.76,  122.44) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1081.63 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:19.908] [info] Track[ 1] ID:  3 | Pos: ( 996.96,  395.73,  143.12) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1082.14 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:20.016] [info] 方位角变化，角度过滤，跳过帧: 2704，角度: 45.61，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:20.017] [info] Track[ 0] ID:  2 | Pos: ( 976.43,  449.94,  122.49) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1082.07 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:20.017] [info] Track[ 1] ID:  3 | Pos: ( 997.38,  395.90,  143.18) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1082.59 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:20.126] [info] 方位角变化，角度过滤，跳过帧: 2705，角度: 48.68，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:20.126] [info] Track[ 0] ID:  2 | Pos: ( 976.83,  450.12,  122.54) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1082.51 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:20.126] [info] Track[ 1] ID:  3 | Pos: ( 997.79,  396.06,  143.24) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1083.04 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:20.235] [info] 方位角变化，角度过滤，跳过帧: 2706，角度: 51.76，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:20.235] [info] Track[ 0] ID:  2 | Pos: ( 977.23,  450.30,  122.59) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1082.95 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:20.235] [info] Track[ 1] ID:  3 | Pos: ( 998.20,  396.23,  143.30) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1083.48 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:20.344] [info] 方位角变化，角度过滤，跳过帧: 2707，角度: 54.86，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:20.344] [info] Track[ 0] ID:  2 | Pos: ( 977.62,  450.49,  122.64) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1083.39 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:20.344] [info] Track[ 1] ID:  3 | Pos: ( 998.62,  396.39,  143.36) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1083.93 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:20.453] [info] 方位角变化，角度过滤，跳过帧: 2708，角度: 57.93，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:20.454] [info] Track[ 0] ID:  2 | Pos: ( 978.02,  450.67,  122.69) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1083.83 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:20.454] [info] Track[ 1] ID:  3 | Pos: ( 999.03,  396.55,  143.42) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1084.38 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:20.570] [warning] 无效帧:2807 模型没有检测到目标，跳过处理
[09-12 17:41:20.570] [info] Track[ 0] ID:  2 | Pos: ( 978.42,  450.85,  122.74) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1084.27 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:20.570] [info] Track[ 1] ID:  3 | Pos: ( 999.44,  396.72,  143.48) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1084.83 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:20.680] [warning] 无效帧:2808 模型没有检测到目标，跳过处理
[09-12 17:41:20.681] [info] Track[ 0] ID:  2 | Pos: ( 978.81,  451.03,  122.79) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1084.71 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:20.681] [info] Track[ 1] ID:  3 | Pos: ( 999.86,  396.88,  143.54) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1085.28 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:20.797] [warning] 无效帧:2809 模型没有检测到目标，跳过处理
[09-12 17:41:20.798] [info] Track[ 0] ID:  2 | Pos: ( 979.21,  451.22,  122.84) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1085.14 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:20.798] [info] Track[ 1] ID:  3 | Pos: (1000.27,  397.05,  143.60) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1085.73 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:20.907] [warning] 无效帧:2810 模型没有检测到目标，跳过处理
[09-12 17:41:20.908] [info] Track[ 0] ID:  2 | Pos: ( 979.61,  451.40,  122.89) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1085.58 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:20.908] [info] Track[ 1] ID:  3 | Pos: (1000.68,  397.21,  143.65) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1086.18 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:21.020] [warning] 无效帧:2811 模型没有检测到目标，跳过处理
[09-12 17:41:21.021] [info] Track[ 0] ID:  2 | Pos: ( 980.00,  451.58,  122.94) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1086.02 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:21.021] [info] Track[ 1] ID:  3 | Pos: (1001.10,  397.37,  143.71) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1086.62 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:21.133] [warning] 无效帧:2812 模型没有检测到目标，跳过处理
[09-12 17:41:21.134] [info] Track[ 0] ID:  2 | Pos: ( 980.40,  451.77,  122.99) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1086.46 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:21.134] [info] Track[ 1] ID:  3 | Pos: (1001.51,  397.54,  143.77) m | Vel: (  8.27,   3.28,   1.19) m/s | R: 1087.07 m | Vr:   8.97 m/s | Az:  21.65° | El:   7.60°
[09-12 17:41:21.245] [info] Frame: 2813, Velo:   8.97, Range: 1151.00, Amaz:  20.94, Elev:   5.30, Altitude: 106.32, X_cor: 1852, Y_cor: 561
[09-12 17:41:21.246] [info] Track[ 0] ID:  2 | Pos: ( 980.80,  451.95,  123.04) m | Vel: (  7.93,   3.65,   1.00) m/s | R: 1086.90 m | Vr:   8.79 m/s | Az:  24.74° | El:   6.50°
[09-12 17:41:21.246] [info] Track[ 1] ID:  3 | Pos: (1070.37,  409.60,  106.33) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1150.99 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:21.360] [info] Frame: 2814, Velo:   8.79, Range: 1136.00, Amaz:  23.80, Elev:   5.50, Altitude: 108.88, X_cor: 1847, Y_cor: 560
[09-12 17:41:21.363] [info] Track[ 0] ID:  2 | Pos: (1034.60,  456.32,  108.88) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1135.99 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:21.363] [info] Track[ 1] ID:  3 | Pos: (1070.79,  409.75,  106.37) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1151.44 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:21.471] [warning] 无效帧:2815 模型没有检测到目标，跳过处理
[09-12 17:41:21.472] [info] Track[ 0] ID:  2 | Pos: (1035.00,  456.49,  108.93) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1136.43 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:21.472] [info] Track[ 1] ID:  3 | Pos: (1071.21,  409.91,  106.41) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1151.89 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:21.584] [warning] 无效帧:2816 模型没有检测到目标，跳过处理
[09-12 17:41:21.585] [info] Track[ 0] ID:  2 | Pos: (1035.40,  456.67,  108.97) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1136.87 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:21.585] [info] Track[ 1] ID:  3 | Pos: (1071.63,  410.07,  106.45) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1152.33 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:21.692] [info] 方位角变化，角度过滤，跳过帧: 2817，角度: 32.6，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:21.693] [info] Track[ 0] ID:  2 | Pos: (1035.80,  456.85,  109.01) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1137.31 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:21.693] [info] Track[ 1] ID:  3 | Pos: (1072.04,  410.23,  106.49) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1152.78 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:21.801] [info] 方位角变化，角度过滤，跳过帧: 2818，角度: 35.67，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:21.801] [info] Track[ 0] ID:  2 | Pos: (1036.20,  457.02,  109.05) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1137.75 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:21.801] [info] Track[ 1] ID:  3 | Pos: (1072.46,  410.39,  106.53) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1153.23 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:21.909] [info] 方位角变化，角度过滤，跳过帧: 2819，角度: 38.76，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:21.909] [info] Track[ 0] ID:  2 | Pos: (1036.60,  457.20,  109.09) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1138.19 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:21.909] [info] Track[ 1] ID:  3 | Pos: (1072.88,  410.55,  106.57) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1153.68 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:22.021] [info] 方位角变化，角度过滤，跳过帧: 2820，角度: 41.61，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:22.021] [info] Track[ 0] ID:  2 | Pos: (1037.00,  457.38,  109.14) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1138.63 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:22.021] [info] Track[ 1] ID:  3 | Pos: (1073.29,  410.71,  106.62) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1154.13 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:22.134] [info] 方位角变化，角度过滤，跳过帧: 2821，角度: 44.68，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:22.135] [info] Track[ 0] ID:  2 | Pos: (1037.40,  457.55,  109.18) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1139.07 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:22.135] [info] Track[ 1] ID:  3 | Pos: (1073.71,  410.87,  106.66) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1154.58 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:22.243] [info] 方位角变化，角度过滤，跳过帧: 2822，角度: 47.79，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:22.243] [info] Track[ 0] ID:  2 | Pos: (1037.80,  457.73,  109.22) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1139.51 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:22.243] [info] Track[ 1] ID:  3 | Pos: (1074.13,  411.03,  106.70) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1155.03 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:22.350] [info] 方位角变化，角度过滤，跳过帧: 2823，角度: 50.85，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:22.350] [info] Track[ 0] ID:  2 | Pos: (1038.20,  457.90,  109.26) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1139.95 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:22.350] [info] Track[ 1] ID:  3 | Pos: (1074.55,  411.19,  106.74) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1155.47 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:22.458] [info] 方位角变化，角度过滤，跳过帧: 2824，角度: 53.92，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:22.458] [info] Track[ 0] ID:  2 | Pos: (1038.60,  458.08,  109.30) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1140.39 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:22.458] [info] Track[ 1] ID:  3 | Pos: (1074.96,  411.35,  106.78) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1155.92 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:22.575] [warning] 无效帧:2455 模型没有检测到目标，跳过处理
[09-12 17:41:22.575] [info] Track[ 0] ID:  2 | Pos: (1039.00,  458.26,  109.35) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1140.83 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:22.575] [info] Track[ 1] ID:  3 | Pos: (1075.38,  411.51,  106.82) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1156.37 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:22.697] [warning] 无效帧:2456 模型没有检测到目标，跳过处理
[09-12 17:41:22.699] [info] Track[ 0] ID:  2 | Pos: (1039.40,  458.43,  109.39) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1141.26 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:22.699] [info] Track[ 1] ID:  3 | Pos: (1075.80,  411.67,  106.86) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1156.82 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:22.820] [warning] 无效帧:2457 模型没有检测到目标，跳过处理
[09-12 17:41:22.821] [info] Track[ 0] ID:  2 | Pos: (1039.80,  458.61,  109.43) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1141.70 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:22.821] [info] Track[ 1] ID:  3 | Pos: (1076.22,  411.83,  106.91) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1157.27 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:22.933] [warning] 无效帧:2458 模型没有检测到目标，跳过处理
[09-12 17:41:22.933] [info] Track[ 0] ID:  2 | Pos: (1040.20,  458.79,  109.47) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1142.14 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:22.933] [info] Track[ 1] ID:  3 | Pos: (1076.63,  411.99,  106.95) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1157.72 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.049] [warning] 无效帧:2459 模型没有检测到目标，跳过处理
[09-12 17:41:23.050] [info] Track[ 0] ID:  2 | Pos: (1040.60,  458.96,  109.51) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1142.58 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:23.050] [info] Track[ 1] ID:  3 | Pos: (1077.05,  412.15,  106.99) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1158.17 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.166] [warning] 无效帧:2460 模型没有检测到目标，跳过处理
[09-12 17:41:23.169] [info] Track[ 0] ID:  2 | Pos: (1041.00,  459.14,  109.56) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1143.02 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:23.169] [info] Track[ 1] ID:  3 | Pos: (1077.47,  412.31,  107.03) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1158.62 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.281] [warning] 无效帧:2461 模型没有检测到目标，跳过处理
[09-12 17:41:23.283] [info] Track[ 0] ID:  2 | Pos: (1041.40,  459.32,  109.60) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1143.46 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:23.283] [info] Track[ 1] ID:  3 | Pos: (1077.88,  412.47,  107.07) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1159.06 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.408] [info] Frame: 2462, Velo:   9.70, Range: 971.00, Amaz:  23.25, Elev:   7.20, Altitude: 121.70, X_cor: 1792, Y_cor: 565
[09-12 17:41:23.409] [info] Track[ 0] ID:  2 | Pos: (1041.80,  459.49,  109.64) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1143.90 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:23.409] [info] Track[ 1] ID:  3 | Pos: (1078.30,  412.63,  107.11) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1159.51 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.522] [info] Frame: 2463, Velo:   9.52, Range: 968.00, Amaz:  26.33, Elev:   5.80, Altitude:  97.82, X_cor: 1791, Y_cor: 564
[09-12 17:41:23.524] [info] Track[ 0] ID:  2 | Pos: (1042.20,  459.67,  109.68) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1144.34 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:23.524] [info] Track[ 1] ID:  3 | Pos: (1078.72,  412.79,  107.15) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1159.96 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.524] [info] Track[ 2] ID:  4 | Pos: ( 863.15,  427.10,   97.85) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.00 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:23.645] [warning] 无效帧:2464 模型没有检测到目标，跳过处理
[09-12 17:41:23.646] [info] Track[ 0] ID:  2 | Pos: (1042.60,  459.85,  109.73) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1144.78 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:23.646] [info] Track[ 1] ID:  3 | Pos: (1079.14,  412.95,  107.20) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1160.41 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.646] [info] Track[ 2] ID:  4 | Pos: ( 863.58,  427.31,   97.89) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.48 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:23.756] [info] 方位角变化，角度过滤，跳过帧: 2465，角度: 31.83，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:23.756] [info] Track[ 0] ID:  2 | Pos: (1043.00,  460.02,  109.77) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1145.22 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:23.757] [info] Track[ 1] ID:  3 | Pos: (1079.55,  413.11,  107.24) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1160.86 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.757] [info] Track[ 2] ID:  4 | Pos: ( 864.00,  427.52,   97.94) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 968.95 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:23.864] [info] 方位角变化，角度过滤，跳过帧: 2466，角度: 34.91，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:23.864] [info] Track[ 0] ID:  2 | Pos: (1043.41,  460.20,  109.81) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1145.66 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:23.864] [info] Track[ 1] ID:  3 | Pos: (1079.97,  413.27,  107.28) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1161.31 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.864] [info] Track[ 2] ID:  4 | Pos: ( 864.43,  427.73,   97.99) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.43 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:23.973] [info] 方位角变化，角度过滤，跳过帧: 2467，角度: 38.01，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:23.974] [info] Track[ 0] ID:  2 | Pos: (1043.81,  460.38,  109.85) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1146.10 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:23.974] [info] Track[ 1] ID:  3 | Pos: (1080.39,  413.43,  107.32) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1161.76 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:23.974] [info] Track[ 2] ID:  4 | Pos: ( 864.85,  427.94,   98.04) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 969.91 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:24.082] [info] 方位角变化，角度过滤，跳过帧: 2468，角度: 41.08，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:24.083] [info] Track[ 0] ID:  2 | Pos: (1044.21,  460.55,  109.89) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1146.54 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:24.083] [info] Track[ 1] ID:  3 | Pos: (1080.80,  413.59,  107.36) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1162.20 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:24.083] [info] Track[ 2] ID:  4 | Pos: ( 865.28,  428.15,   98.09) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.38 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:24.192] [info] 方位角变化，角度过滤，跳过帧: 2469，角度: 44.15，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:24.192] [info] Track[ 0] ID:  2 | Pos: (1044.61,  460.73,  109.94) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1146.98 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:24.193] [info] Track[ 1] ID:  3 | Pos: (1081.22,  413.75,  107.40) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1162.65 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:24.193] [info] Track[ 2] ID:  4 | Pos: ( 865.70,  428.36,   98.13) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 970.86 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:24.309] [info] 方位角变化，角度过滤，跳过帧: 2470，角度: 47.25，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:24.310] [info] Track[ 0] ID:  2 | Pos: (1045.01,  460.91,  109.98) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1147.42 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:24.310] [info] Track[ 1] ID:  3 | Pos: (1081.64,  413.91,  107.44) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1163.10 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:24.310] [info] Track[ 2] ID:  4 | Pos: ( 866.13,  428.57,   98.18) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.33 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:24.420] [info] 方位角变化，角度过滤，跳过帧: 2471，角度: 50.31，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:24.421] [info] Track[ 0] ID:  2 | Pos: (1045.41,  461.08,  110.02) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1147.86 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:24.421] [info] Track[ 1] ID:  3 | Pos: (1082.06,  414.07,  107.48) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1163.55 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:24.421] [info] Track[ 2] ID:  4 | Pos: ( 866.55,  428.78,   98.23) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 971.81 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:24.533] [info] 方位角变化，角度过滤，跳过帧: 2472，角度: 53.39，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:24.534] [info] Track[ 0] ID:  2 | Pos: (1045.81,  461.26,  110.06) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1148.30 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:24.534] [info] Track[ 1] ID:  3 | Pos: (1082.47,  414.22,  107.53) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1164.00 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:24.534] [info] Track[ 2] ID:  4 | Pos: ( 866.97,  428.99,   98.28) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.29 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:24.643] [info] 方位角变化，角度过滤，跳过帧: 2473，角度: 56.49，目标数量: 0, 列数据段数量: 0 
[09-12 17:41:24.644] [info] Track[ 0] ID:  2 | Pos: (1046.21,  461.44,  110.10) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1148.74 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:24.644] [info] Track[ 1] ID:  3 | Pos: (1082.89,  414.38,  107.57) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1164.45 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:24.644] [info] Track[ 2] ID:  4 | Pos: ( 867.40,  429.20,   98.33) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 972.76 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:24.764] [warning] 无效帧:2572 模型没有检测到目标，跳过处理
[09-12 17:41:24.765] [info] Track[ 0] ID:  2 | Pos: (1046.61,  461.61,  110.15) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1149.18 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:24.765] [info] Track[ 1] ID:  3 | Pos: (1083.31,  414.54,  107.61) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1164.90 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:24.765] [info] Track[ 2] ID:  4 | Pos: ( 867.82,  429.41,   98.38) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.24 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:24.878] [warning] 无效帧:2573 模型没有检测到目标，跳过处理
[09-12 17:41:24.879] [info] Track[ 0] ID:  2 | Pos: (1047.01,  461.79,  110.19) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1149.61 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:24.879] [info] Track[ 1] ID:  3 | Pos: (1083.73,  414.70,  107.65) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1165.34 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:24.879] [info] Track[ 2] ID:  4 | Pos: ( 868.25,  429.62,   98.42) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 973.71 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:25.000] [warning] 无效帧:2574 模型没有检测到目标，跳过处理
[09-12 17:41:25.000] [info] Track[ 0] ID:  2 | Pos: (1047.41,  461.96,  110.23) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1150.05 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:25.000] [info] Track[ 1] ID:  3 | Pos: (1084.14,  414.86,  107.69) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1165.79 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:25.000] [info] Track[ 2] ID:  4 | Pos: ( 868.67,  429.84,   98.47) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.19 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:25.120] [warning] 无效帧:2575 模型没有检测到目标，跳过处理
[09-12 17:41:25.121] [info] Track[ 0] ID:  2 | Pos: (1047.81,  462.14,  110.27) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1150.49 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:25.121] [info] Track[ 1] ID:  3 | Pos: (1084.56,  415.02,  107.73) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1166.24 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:25.121] [info] Track[ 2] ID:  4 | Pos: ( 869.10,  430.05,   98.52) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 974.67 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:25.237] [warning] 无效帧:2576 模型没有检测到目标，跳过处理
[09-12 17:41:25.238] [info] Track[ 0] ID:  2 | Pos: (1048.21,  462.32,  110.31) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1150.93 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:25.238] [info] Track[ 1] ID:  3 | Pos: (1084.98,  415.18,  107.77) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1166.69 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:25.238] [info] Track[ 2] ID:  4 | Pos: ( 869.52,  430.26,   98.57) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.14 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:25.359] [warning] 无效帧:2577 模型没有检测到目标，跳过处理
[09-12 17:41:25.359] [info] Track[ 0] ID:  2 | Pos: (1048.61,  462.49,  110.36) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1151.37 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:25.359] [info] Track[ 1] ID:  3 | Pos: (1085.39,  415.34,  107.82) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1167.14 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:25.359] [info] Track[ 2] ID:  4 | Pos: ( 869.95,  430.47,   98.62) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 975.62 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
[09-12 17:41:25.492] [warning] 无效帧:2578 模型没有检测到目标，跳过处理
[09-12 17:41:25.492] [info] Track[ 0] ID:  2 | Pos: (1049.01,  462.67,  110.40) m | Vel: (  8.01,   3.53,   0.84) m/s | R: 1151.81 m | Vr:   8.79 m/s | Az:  23.80° | El:   5.50°
[09-12 17:41:25.492] [info] Track[ 1] ID:  3 | Pos: (1085.81,  415.50,  107.86) m | Vel: (  8.34,   3.19,   0.83) m/s | R: 1167.59 m | Vr:   8.97 m/s | Az:  20.94° | El:   5.30°
[09-12 17:41:25.492] [info] Track[ 2] ID:  4 | Pos: ( 870.37,  430.68,   98.66) m | Vel: (  8.49,   4.20,   0.96) m/s | R: 976.09 m | Vr:   9.52 m/s | Az:  26.33° | El:   5.80°
