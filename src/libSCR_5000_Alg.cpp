#include "libSCR_5000_Alg.hpp"
#include "preprocess.hpp"
#include "postprocess.hpp"
#include "infer_engine.hpp"
#include "PointTracker.hpp"
#include "logger.hpp"
#include "config.hpp"
#include "fft_gpu.hpp"
#include "resource_manager.hpp"
#include "memory_pool.hpp"
#include "unified_resource_manager.hpp"

#include "TutorialConfig.h"

#include <cuda_runtime_api.h>
#include <opencv2/opencv.hpp>
#include <chrono>
#include <iostream>
#include <fstream>
#include <memory>
#include <ctime>
#include <cstring>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <stdexcept>

using namespace scr5000;

// 库初始化状态管理
static std::atomic<bool> s_lib_initialized{false};
static std::atomic<bool> s_init_failed{false};
static std::mutex s_init_mutex;
static std::mutex dete_track_mutex;

// ==================== 内存监控工具类 ====================
class MemoryMonitor {
public:
    static void logMemoryUsage(const std::string& checkpoint) {
        try {
            size_t free_mem = 0, total_mem = 0;
            cudaError_t status = cudaMemGetInfo(&free_mem, &total_mem);
            if (status == cudaSuccess) {
                size_t used_mem = total_mem - free_mem;
                spdlog::debug("- 显存 - {}: 使用 {:.1f} MB",
                            checkpoint,
                            used_mem / (1024.0 * 1024.0));
            } else {
                spdlog::warn("Failed to get GPU memory info at {}: {}", checkpoint, cudaGetErrorString(status));
            }
        } catch (const std::exception& e) {
            spdlog::warn("Exception in memory monitoring at {}: {}", checkpoint, e.what());
        }
    }

    static void logCPUMemoryUsage(const std::string& checkpoint) {
        try {
            std::ifstream status_file("/proc/self/status");
            std::string line;
            while (std::getline(status_file, line)) {
                if (line.find("VmRSS:") == 0) {
                    std::istringstream iss(line);
                    std::string label;
                    long kb = 0;
                    iss >> label >> kb;
                    double mb = kb / 1024.0;
                    spdlog::debug("- 内存 - {}: 使用 {:.1f} MB", checkpoint, mb);
                    break;
                }
            }
        } catch (const std::exception& e) {
            spdlog::warn("Exception in CPU memory monitoring at {}: {}", checkpoint, e.what());
        }
    }

    static void logTotalMemoryUsage(const std::string& checkpoint) {
        try {
            /* ---------- GPU 显存 ---------- */
            size_t gpu_free = 0, gpu_total = 0;
            double gpu_mb = 0.0;
            cudaError_t st = cudaMemGetInfo(&gpu_free, &gpu_total);
            if (st == cudaSuccess) {
                gpu_mb = (gpu_total - gpu_free) / (1024.0 * 1024.0);
            } else {
                spdlog::warn("Failed to get GPU memory: {}", cudaGetErrorString(st));
            }

            /* ---------- CPU 物理内存 ---------- */
            double cpu_mb = 0.0;
            std::ifstream status_file("/proc/self/status");
            std::string line;
            while (std::getline(status_file, line)) {
                if (line.find("VmRSS:") == 0) {
                    std::istringstream iss(line);
                    std::string label;
                    long kb = 0;
                    iss >> label >> kb;
                    cpu_mb = kb / 1024.0;
                    break;
                }
            }

            /* ---------- 打印总和 ---------- */
            double total_mb = gpu_mb + cpu_mb;
            spdlog::debug("- 总内存 - {}: 显存 {:.1f} MB + 内存 {:.1f} MB = {:.1f} MB",
                         checkpoint, gpu_mb, cpu_mb, total_mb);

        } catch (const std::exception& e) {
            spdlog::warn("Exception in total memory monitoring at {}: {}", checkpoint, e.what());
        }
    }

};

// 验证 InternalDetectionData 结构的完整性
bool validateDetectionData(const InternalDetectionData* detection_data) {
    if (!detection_data) {
        return false;
    }

    const int MAX_REASONABLE_COUNT = 10000;

    // 检查计数字段是否合理
    if (detection_data->num_detections < 0 || detection_data->num_detections > MAX_REASONABLE_COUNT ||
        detection_data->num_segments < 0 || detection_data->num_segments > MAX_REASONABLE_COUNT) {
        spdlog::error("检测数据计数异常: num_detections={}, num_segments={}",
                      detection_data->num_detections, detection_data->num_segments);
        return false;
    }

    // 检查指针和计数的一致性
    if (detection_data->num_detections > 0 && !detection_data->detection_results) {
        spdlog::error("检测结果计数>0但指针为空");
        return false;
    }

    if (detection_data->num_segments > 0 && !detection_data->column_segments) {
        spdlog::error("列数据段计数>0但指针为空");
        return false;
    }

    // 检查计数一致性
    if (detection_data->num_detections != detection_data->num_segments) {
        spdlog::error("检测结果数量({})与列数据段数量({})不匹配",
                      detection_data->num_detections, detection_data->num_segments);
        return false;
    }

    return true;
}

int copyFrameHeaders(InternalDetectionData* detection_data,
                     const FrameHeader_Alg* S_head_ptr,
                     size_t max_possible)
{
    if (!detection_data || !S_head_ptr) {
        spdlog::error("Invalid pointer passed to copyFrameHeaders");
        return -1;
    }

    const uint32_t FRAME_MARKER = 2863289685u;

    // 先收集符合条件的帧头
    std::vector<FrameHeader_Alg> valid_headers;
    valid_headers.reserve(max_possible);  // 预分配，避免多次扩容

    for (size_t i = 0; i < max_possible; ++i) {
        if (S_head_ptr[i].marker == FRAME_MARKER &&
            S_head_ptr[i].channel == 1)
        {
            valid_headers.push_back(S_head_ptr[i]);
        }
    }

    if (valid_headers.empty()) {
        spdlog::warn("No valid frame headers found");
        detection_data->S_head = nullptr;
        return 0;
    }

    // 分配新内存（旧内存先释放，避免泄漏）
    delete[] detection_data->S_head;
    detection_data->S_head = nullptr;

    try {
        detection_data->S_head = new FrameHeader_Alg[valid_headers.size()];
        std::copy(valid_headers.begin(), valid_headers.end(), detection_data->S_head);
    } catch (const std::bad_alloc& e) {
        spdlog::error("Failed to allocate memory for frame headers: {}", e.what());
        return -1;
    }

    return static_cast<int>(valid_headers.size());
}


// ==================== 公共API函数 ====================
ALGORITHM_API void GetVersionInfo(AlgorithmVersion* version_info) {
    static const AlgorithmVersion version = {
        ALGO_VERSION_MAJOR,
        ALGO_VERSION_MINOR,
        ALGO_VERSION_PATCH,
        ALGO_VERSION_STRING,
        ALGO_BUILD_TIMESTAMP
    };
    if (version_info) {
        *version_info = version;
    }
}

// 算法库初始化
ALGORITHM_API int InitializeAlgorithmLibrary(const char* config_path) {

    if (s_lib_initialized.load(std::memory_order_acquire)) {
        std::cout << "算法库已经初始化" << std::endl;
        return 0;
    }

    if (s_init_failed.load(std::memory_order_acquire)) {
        std::cout << "算法库初始化失败" << std::endl;
        return -1;
    }

    std::lock_guard<std::mutex> lock(s_init_mutex);

    // 双重检查锁定
    if (s_lib_initialized.load(std::memory_order_relaxed)) {
        std::cout << "算法库已经初始化" << std::endl;
        return 0;
    }

    if (s_init_failed.load(std::memory_order_relaxed)) {
        std::cout << "算法库初始化失败" << std::endl;
        return -1;
    }

    if (!config_path) {
        std::cout << "配置文件路径错误" << std::endl;
        return -1;
    }

    // 首先直接从配置文件初始化日志系统
    if (!initLoggerFromConfig(config_path)) {
        std::cout << "从配置文件初始化日志失败，使用控制台模式" << std::endl;
        initLogger();
    }
    spdlog::debug("=====算法库初始化开始=====");

    try {
        // 初始化统一资源管理器
        if (!scr5000::initializeResourceManager(config_path)) {
            spdlog::error("统一资源管理器初始化失败");
            s_init_failed.store(true, std::memory_order_release);
            return -3;
        }

        auto* resource_manager = scr5000::getResourceManager();
        if (!resource_manager) {
            spdlog::error("无法获取资源管理器");
            s_init_failed.store(true, std::memory_order_release);
            return -2;
        }

        auto* config_manager = resource_manager->getConfigManager();
        if (!config_manager) {
            spdlog::error("无法获取配置管理器");
            s_init_failed.store(true, std::memory_order_release);
            return -2;
        }

        // 重新初始化日志系统以应用配置文件中的日志等级设置
        try {
            auto log_path_opt = config_manager->get<std::string>("/io_settings/logging/log_file_path");
            auto log_level_opt = config_manager->get<std::string>("/io_settings/logging/log_level");

            if (log_path_opt && log_level_opt) {
                spdlog::level::level_enum file_level = stringToLogLevel(*log_level_opt);
                spdlog::info("重新配置日志系统 - 路径: {}, 等级: {}", *log_path_opt, *log_level_opt);
                initLogger(*log_path_opt, file_level);
            } else {
                spdlog::warn("配置文件中缺少日志设置，使用默认配置");
            }
        } catch (const std::exception& e) {
            spdlog::warn("重新配置日志系统失败: {}", e.what());
        }

        // 初始化自定义插件
        // initializeCustomPlugins();

        // 加载TensorRT引擎
        std::string engine_path = *config_manager->get<std::string>("/io_settings/engine_paths");
        std::ifstream test_file(engine_path);
        if (test_file.good()) {
            auto* gpu_manager = resource_manager->getGPUManager();
            if (!gpu_manager->loadTensorRTEngine(engine_path)) {
                spdlog::error("Failed to load TensorRT engine from: {}", engine_path);
                s_init_failed.store(true, std::memory_order_release);
                return -4;
            }

            // 获取引擎信息并分配缓冲区
            TensorRTEngineRAII* tensorrt_engine = gpu_manager->getTensorRTEngine();
            if (!tensorrt_engine || !tensorrt_engine->is_valid()) {
                spdlog::error("TensorRT engine is not valid");
                s_init_failed.store(true, std::memory_order_release);
                return -5;
            }

            nvinfer1::ICudaEngine* engine = tensorrt_engine->getEngine();
            const auto input_dims = engine->getBindingDimensions(0);
            const auto output_dims = engine->getBindingDimensions(1);

            const size_t input_size = input_dims.d[4] * input_dims.d[2] * input_dims.d[3];
            const size_t output_size = output_dims.d[2] * output_dims.d[3];

            spdlog::info("模型输入维度: [{}x{}x{}x{}]", input_dims.d[1], input_dims.d[2], input_dims.d[3], input_dims.d[4]);

            // 分配GPU缓冲区
            if (!gpu_manager->allocateBuffers(input_size, output_size)) {
                spdlog::error("Failed to allocate GPU buffers");
                s_init_failed.store(true, std::memory_order_release);
                return -6;
            }

            // 预分配CPU内存
            auto& output_prob = resource_manager->getOutputProb();
            auto& sample_input = resource_manager->getSampleInput();

            try {
                output_prob.resize(output_size);
                sample_input.resize(1024 * 1024 * 2);
            } catch (const std::bad_alloc& e) {
                spdlog::error("Failed to allocate CPU memory: {}", e.what());
                s_init_failed.store(true, std::memory_order_release);
                return -7;
            }
        } else {
            spdlog::error("Engine file not found: {}", engine_path);
            s_init_failed.store(true, std::memory_order_release);
            return -8;
        }

        // 加载查表数据
        std::string table_path = *config_manager->get<std::string>("/io_settings/table_paths");
        std::ifstream table_file(table_path);
        if (table_file.good()) {
            spdlog::info("加载俯仰角查表数据: {}", table_path);
            loadHechaTable_(table_path);
        } else {
            spdlog::warn("Could not find hecha_table.csv, using default values");
        }

        // 记录初始化后的内存使用情况
        MemoryMonitor::logMemoryUsage("算法初始化完成");
        MemoryMonitor::logCPUMemoryUsage("算法初始化完成");
        // MemoryMonitor::logTotalMemoryUsage("算法初始化完成");

        // 标记为已初始化
        s_lib_initialized.store(true, std::memory_order_release);
        spdlog::debug("=====算法库初始化完成=====");

        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception in InitializeAlgorithmLibrary: {}", e.what());
        s_init_failed.store(true, std::memory_order_release);
        return -10;
    }
}

// ==================== 目标检测算法实现 ====================
ALGORITHM_API int TargetDetection(
    char* input_head_data,
    char* input_data,
    InternalDetectionData* detection_data
) {

    std::lock_guard<std::mutex> lk(dete_track_mutex);

    spdlog::debug("=====开始目标检测=====");

    // 参数验证
    if (!input_data || !detection_data) {
        spdlog::error("Invalid input parameters for TargetDetection");
        return -1;
    }

    // 获取资源管理器
    auto* resource_manager = scr5000::getResourceManager();
    if (!resource_manager || !resource_manager->isInitialized()) {
        spdlog::error("Resource manager not initialized");
        return -1;
    }

    auto* config_manager = resource_manager->getConfigManager();
    auto frame_view = getFrameDataView(input_head_data, input_data,
                                      *config_manager->get<int>("/algorithm_settings/detection/frame_idx"));
    const FrameHeader_Alg* S_head_ptr = frame_view.head_S;
    const float* S_data = frame_view.data_S;
    const float* D_data = frame_view.data_D;

    // 初始化输出参数 - 确保所有字段都被正确初始化
    detection_data->detection_results = nullptr;
    detection_data->num_detections = 0;
    detection_data->column_segments = nullptr;
    detection_data->num_segments = 0;
    detection_data->S_head = nullptr;
    
    // TODO：复制帧头数据以避免外部释放导致的数据丢失
    int num_headers = copyFrameHeaders(detection_data, S_head_ptr, 1024);
    spdlog::debug("提取到 {} 个有效帧头", num_headers);

    //  隔4帧处理一次
    // if (S_head_ptr[0].frame_num % 4 != 0) {
    //     spdlog::info("跳过帧: {}", S_head_ptr[0].frame_num);
    //     return 0;
    // }

    // 检查帧头角度，是否在0～60度，不在跳过处理
    // if (S_head_ptr[0].angle / 100.0f > *config_manager->get<int>("/algorithm_settings/detection/max_angle")) {
    //     spdlog::info("跳过帧: {}，角度为: {}", S_head_ptr[0].frame_num, S_head_ptr[0].angle / 100.0f);
    //     return 0;
    // }

    // 检查帧头帧号，取余后是否在0~20，不在跳过处理
    // if (S_head_ptr[0].frame_num % 120 > *config_manager->get<int>("/algorithm_settings/detection/max_frame")) {
    //     spdlog::info("跳过帧: {}, 方位角：{}", S_head_ptr[0].frame_num, S_head_ptr[0].angle / 100.0f);
    //     return 0;
    // }

    // 获取当前帧的方位角
    float current_angle = S_head_ptr[0].angle / 100.0f;
    static float prev_angle = -1.0f;
    static bool angle_unchanged = false;

    // 更新方位角变化状态
    if (prev_angle >= 0) {
        angle_unchanged = (std::abs(current_angle - prev_angle) <= 0.00001f);
    }
    prev_angle = current_angle;

    // 根据策略选择过滤条件
    bool should_skip = false;
    std::string skip_reason;

    if (angle_unchanged) {
        // 方位角不变时：只使用帧数过滤
        if (S_head_ptr[0].frame_num % 120 > *config_manager->get<int>("/algorithm_settings/detection/max_frame")) {
            should_skip = true;
            skip_reason = "方位角未变化，帧数过滤";
        }
    } else {
        // 方位角变化时：使用角度过滤
        if (current_angle > *config_manager->get<int>("/algorithm_settings/detection/max_angle")) {
            should_skip = true;
            skip_reason = "方位角变化，角度过滤";
        }
    }

    if (should_skip) {
        spdlog::info("{}，跳过帧: {}，角度: {}，目标数量: {}, 列数据段数量: {} ",
                     skip_reason, S_head_ptr[0].frame_num, current_angle, detection_data->num_detections, detection_data->num_segments
        );
        // 确保跳过帧时所有字段都被正确重置
        detection_data->detection_results = nullptr;
        detection_data->num_detections = 0;
        detection_data->column_segments = nullptr;
        detection_data->num_segments = 0;
        return 0;
    }

    try {
        // 获取各种管理器和缓冲区
        auto* gpu_manager = resource_manager->getGPUManager();
        auto& sample_input = resource_manager->getSampleInput();
        auto& output_prob = resource_manager->getOutputProb();

        // 预处理：采样、归一化和FFT处理
        auto t0 = std::chrono::high_resolution_clock::now();
        sample_and_normalize(S_data, 1024 * 2048 * 2, 
            *config_manager->get<float>("/algorithm_settings/detection/real_off"),
            *config_manager->get<float>("/algorithm_settings/detection/real_scl"),
            *config_manager->get<float>("/algorithm_settings/detection/imag_off"),
            *config_manager->get<float>("/algorithm_settings/detection/imag_scl"),
            sample_input);
        auto t1 = std::chrono::high_resolution_clock::now();

        // 获取GPU缓冲区和流
        CudaMemoryRAII<float>* input_buffer = gpu_manager->getInputBuffer();
        CudaMemoryRAII<float>* output_buffer = gpu_manager->getOutputBuffer();
        CudaStreamRAII* stream = gpu_manager->getMainStream();
        TensorRTEngineRAII* tensorrt_engine = gpu_manager->getTensorRTEngine();

        if (!input_buffer->is_valid() || !output_buffer->is_valid() ||
            !stream->is_valid() || !tensorrt_engine->is_valid()) {
            spdlog::error("GPU resources not properly initialized");
            return -1;
        }

        // 上传数据到GPU
        cudaError_t cuda_status = cudaMemcpyAsync(
            input_buffer->get(),
            sample_input.data(),
            sample_input.size() * sizeof(float),
            cudaMemcpyHostToDevice,
            stream->get()
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy input data to GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }

        // 执行推理
        void* buffers[2] = {input_buffer->get(), output_buffer->get()};
        bool inference_result = tensorrt_engine->getContext()->enqueueV2(buffers, stream->get(), nullptr);
        if (!inference_result) {
            spdlog::error("TensorRT inference failed");
            return -1;
        }

        // 下载结果
        cuda_status = cudaMemcpyAsync(
            output_prob.data(),
            output_buffer->get(),
            output_prob.size() * sizeof(float),
            cudaMemcpyDeviceToHost,
            stream->get()
        );
        if (cuda_status != cudaSuccess) {
            spdlog::error("Failed to copy output data from GPU: {}", cudaGetErrorString(cuda_status));
            return -1;
        }

        stream->synchronize();
        auto t2 = std::chrono::high_resolution_clock::now();

        // 基本后处理：二值化和连通组件分析
        auto centers = post_process_combined(output_prob.data(), 512, 1024,
            *config_manager->get<int>("/algorithm_settings/detection/threshold"),
            *config_manager->get<float>("/algorithm_settings/detection/max_ratio"),
            *config_manager->get<int>("/algorithm_settings/detection/min_area"));

        if (centers.empty()) {
            spdlog::warn("无效帧:{} 模型没有检测到目标，跳过处理", S_head_ptr[0].frame_num);
            // 确保返回时数据结构是干净的
            detection_data->detection_results = nullptr;
            detection_data->num_detections = 0;
            detection_data->column_segments = nullptr;
            detection_data->num_segments = 0;
            return 0;
        }

        if (centers[0].first == -1) {
            spdlog::warn("异常帧:{} 目标过多，跳过处理", S_head_ptr[0].frame_num);
            // 确保返回时数据结构是干净的
            detection_data->detection_results = nullptr;
            detection_data->num_detections = 0;
            detection_data->column_segments = nullptr;
            detection_data->num_segments = 0;
            return 0;
        }

        auto t3 = std::chrono::high_resolution_clock::now();

        // 分配输出内存
        detection_data->num_detections = centers.size();
        if (detection_data->num_detections > 0) {
            try {
                detection_data->detection_results = new DetectionResult[detection_data->num_detections];
            } catch (const std::bad_alloc& e) {
                spdlog::error("Failed to allocate memory for detection results: {}", e.what());
                if (detection_data->S_head) {
                    delete[] detection_data->S_head;
                    detection_data->S_head = nullptr;
                }
                detection_data->num_detections = 0;
                return -1;
            }
        } else {
            detection_data->detection_results = nullptr;
        }

        spdlog::debug("检测到 {} 个目标中心点", detection_data->num_detections);

        // 填充轻量化检测结果 - 仅包含基本信息
        for (size_t i = 0; i < centers.size(); ++i) {
            detection_data->detection_results[i].row = centers[i].second;  // y坐标对应行
            detection_data->detection_results[i].col = centers[i].first;   // x坐标对应列
            detection_data->detection_results[i].frame = S_head_ptr[0].frame_num; // 从帧头获取帧号

            // spdlog::info("检测点[{}]: 行={}, 列={}, 帧={}", i,
            //              detection_data->detection_results[i].row, detection_data->detection_results[i].col, detection_data->detection_results[i].frame);
        }

        // 提取列数据段供跟踪函数使用
        spdlog::debug("开始提取列数据段，共 {} 个中心点", centers.size());
        detection_data->num_segments = centers.size();
        try {
            detection_data->column_segments = new ColumnSegmentData[detection_data->num_segments];
        } catch (const std::bad_alloc& e) {
            spdlog::error("Failed to allocate memory for column segments: {}", e.what());
            // 清理已分配的检测结果内存
            if (detection_data->detection_results) {
                delete[] detection_data->detection_results;
                detection_data->detection_results = nullptr;
            }
            if (detection_data->S_head) {
                delete[] detection_data->S_head;
                detection_data->S_head = nullptr;
            }
            detection_data->num_detections = 0;
            detection_data->num_segments = 0;
            return -1;
        }

        for (size_t i = 0; i < centers.size(); ++i) {
            int col = centers[i].first;
            int row = centers[i].second;
            try {
                detection_data->column_segments[i] = extractColumnSegmentData(S_data, D_data, col, row);
                spdlog::debug("检测点[{}]: 帧号={}, 列={}, 行={}, 段长度={}", i, detection_data->detection_results[i].frame, col, row, detection_data->column_segments[i].segment_length);
            } catch (const std::exception& e) {
                spdlog::error("Failed to extract column segment for center {}: {}", i, e.what());
                // 清理已分配的内存
                for (size_t j = 0; j < i; ++j) {
                    delete[] detection_data->column_segments[j].data_S;
                    delete[] detection_data->column_segments[j].data_D;
                }
                delete[] detection_data->column_segments;
                detection_data->column_segments = nullptr;
                if (detection_data->detection_results) {
                    delete[] detection_data->detection_results;
                    detection_data->detection_results = nullptr;
                }
                if (detection_data->S_head) {
                    delete[] detection_data->S_head;
                    detection_data->S_head = nullptr;
                }
                detection_data->num_detections = 0;
                detection_data->num_segments = 0;
                return -1;
            }
        }

        auto t4 = std::chrono::high_resolution_clock::now();

        // 记录性能信息
        int t_pre = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
        int t_inf = std::chrono::duration_cast<std::chrono::milliseconds>(t2 - t1).count();
        int t_post = std::chrono::duration_cast<std::chrono::milliseconds>(t3 - t2).count();
        int t_extract = std::chrono::duration_cast<std::chrono::milliseconds>(t4 - t3).count();
        int total = t_pre + t_inf + t_post + t_extract;

        spdlog::debug("目标检测耗时(ms): 预处理:{} 推理:{} 后处理:{} 提取:{} 总:{} (FPS:{:.2f})",
                     t_pre, t_inf, t_post, t_extract, total, 1000.0 / total);

        // 记录目标检测后的内存使用情况
        // MemoryMonitor::logMemoryUsage("目标检测完成");
        // MemoryMonitor::logCPUMemoryUsage("目标检测完成");

        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetDetection: {}", e.what());
        // 清理已分配的内存
        if (detection_data->detection_results) {
            delete[] detection_data->detection_results;
            detection_data->detection_results = nullptr;
        }
        if (detection_data->column_segments) {
            for (int i = 0; i < detection_data->num_segments; ++i) {
                delete[] detection_data->column_segments[i].data_S;
                delete[] detection_data->column_segments[i].data_D;
            }
            delete[] detection_data->column_segments;
            detection_data->column_segments = nullptr;
        }
        if (detection_data->S_head) {
            delete[] detection_data->S_head;
            detection_data->S_head = nullptr;
        }
        detection_data->num_detections = 0;
        detection_data->num_segments = 0;
        return -1;
    }
}

// ==================== 目标跟踪算法实现 ====================
ALGORITHM_API void TargetTracking(
    InternalDetectionData* detection_data,
    TrackingResult* tracking_results,
    int* num_tracks
) {

    std::lock_guard<std::mutex> lk(dete_track_mutex);

    spdlog::debug("=====开始目标跟踪======");

    if (!validateDetectionData(detection_data)) {
        spdlog::error("检测数据验证失败，数据可能已损坏");
        // 安全地重置数据结构
        if (detection_data) {
            detection_data->detection_results = nullptr;
            detection_data->num_detections = 0;
            detection_data->column_segments = nullptr;
            detection_data->num_segments = 0;
            if (detection_data->S_head) {
                delete[] detection_data->S_head;
                detection_data->S_head = nullptr;
            }
        }
        *num_tracks = 0;
        return;
    }

    spdlog::debug("检测数据: num_detections={}, num_segments={}",
                 detection_data->num_detections, detection_data->num_segments);
    

    // TODO：没有检测到目标
    // if (!detection_data || detection_data->num_detections <= 0) {
    //     spdlog::warn("没有检测到目标");
    //     ReleaseInternalDetectionData(detection_data);
    //     *num_tracks = 0;
    //     return;
    // }

    // 参数验证
    if (!tracking_results || !num_tracks) {
        spdlog::error("Invalid input parameters for TargetTracking");
        ReleaseInternalDetectionData(detection_data);
        *num_tracks = 0;
        return;
    }

    // 验证检测数据的完整性
    // if (!detection_data->detection_results || !detection_data->S_head || !detection_data->column_segments ||
    //     detection_data->num_segments != detection_data->num_detections) {
    //     spdlog::error("Invalid detection data for TargetTracking");
    //     ReleaseInternalDetectionData(detection_data);
    //     *num_tracks = 0;

    //     return;
    // }

    try {
        // 将检测结果转换为中心点格式
        std::vector<std::pair<int, int>> centers;
        centers.reserve(detection_data->num_detections);
        for (int i = 0; i < detection_data->num_detections; ++i) {
            centers.emplace_back(detection_data->detection_results[i].col, detection_data->detection_results[i].row);
            spdlog::debug("处理检测点[{}]: 列={}, 行={}", i, detection_data->detection_results[i].col, detection_data->detection_results[i].row);
        }

        // 获取资源管理器
        auto* resource_manager = scr5000::getResourceManager();
        if (!resource_manager || !resource_manager->isInitialized()) {
            spdlog::error("Resource manager not initialized");
            ReleaseInternalDetectionData(detection_data);
            *num_tracks = 0;
            return;
        }

        auto* fft_optimizer = resource_manager->getFFTOptimizer();

        // 使用传入的列数据段进行FFT处理
        std::vector<std::complex<float>> S_complexData;
        std::vector<std::complex<float>> D_complexData;
        S_complexData.reserve(detection_data->num_segments);
        D_complexData.reserve(detection_data->num_segments);

        for (int i = 0; i < detection_data->num_segments; ++i) {
            const auto& segment = detection_data->column_segments[i];

            // 对S通道和D通道数据分别执行FFT
            try {
                // 将S通道数据转换为cufftComplex格式
                std::vector<cufftComplex> S_segment_data(segment.segment_length);
                for (int j = 0; j < segment.segment_length; ++j) {
                    S_segment_data[j].x = segment.data_S[j * 2];     // 实部
                    S_segment_data[j].y = segment.data_S[j * 2 + 1]; // 虚部
                }

                // 将D通道数据转换为cufftComplex格式
                std::vector<cufftComplex> D_segment_data(segment.segment_length);
                for (int j = 0; j < segment.segment_length; ++j) {
                    D_segment_data[j].x = segment.data_D[j * 2];     // 实部
                    D_segment_data[j].y = segment.data_D[j * 2 + 1]; // 虚部
                }

                // 执行FFT处理
                auto S_result = fft_optimizer->performFFTOnSegment(S_segment_data, segment.row, segment.segment_start);
                auto D_result = fft_optimizer->performFFTOnSegment(D_segment_data, segment.row, segment.segment_start);

                S_complexData.push_back(S_result);
                D_complexData.push_back(D_result);

            } catch (const std::exception& e) {
                spdlog::error("FFT处理失败[{}]: {}", i, e.what());
                // 使用原始数据作为备用
                int local_row = segment.row - segment.segment_start;
                if (local_row >= 0 && local_row < segment.segment_length) {
                    S_complexData.emplace_back(segment.data_S[local_row * 2], segment.data_S[local_row * 2 + 1]);
                    D_complexData.emplace_back(segment.data_D[local_row * 2], segment.data_D[local_row * 2 + 1]);
                } else {
                    S_complexData.emplace_back(0.0f, 0.0f);
                    D_complexData.emplace_back(0.0f, 0.0f);
                }
            }
        }

        // 计算俯仰角、方位角、距离等信息
        auto results = computeElevationAngles_GPU(detection_data->S_head, S_complexData, D_complexData, centers, 1024, 2048);

        spdlog::debug("FFT和俯仰角计算完成, 得到 {} 个结果：", results.size());

        for (const auto& [vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col] : results) {
            spdlog::info("Frame: {}, Velo: {:>6.2f}, Range: {:>6.2f}, Amaz: {:>6.2f}, Elev: {:>6.2f}, Altitude: {:>6.2f}, X_cor: {}, Y_cor: {}", frame, fMV, fMR, fMA, fME, z, row, col);
        }

        // 将计算结果转换为Point格式
        std::vector<Point> current_detections;
        current_detections.reserve(results.size());

        for (const auto& [vx, vy, vz, x, y, z, fMV, fMR, fMA, fME, frame, row, col] : results) {
            Point p;
            p.position[0] = x;
            p.position[1] = y;
            p.position[2] = z;
            p.velocity[0] = vx;
            p.velocity[1] = vy;
            p.velocity[2] = vz;
            p.type = 1; // 默认类型
            p.frame = frame;
            p.label = -1; // 初始化标签
            current_detections.push_back(p);
        }

        auto* config_manager = resource_manager->getConfigManager();
        auto* tracker = resource_manager->getTracker();
        // auto& current_group_detections = resource_manager->getCurrentGroupDetections();
        auto& group_start_frame = resource_manager->getGroupStartFrame();
        auto& prev_azimuth = resource_manager->getPrevAzimuth();
        auto& azimuth_unchanged = resource_manager->getAzimuthUnchanged();
        const int frames_per_group = resource_manager->getFramesPerGroup();
        const float invalid_azimuth = resource_manager->getInvalidAzimuth();

        std::vector<Point> clustered;

        if (!current_detections.empty()) {
            // 聚类检测结果
            clustered = clusterDetections_DBSCAN(current_detections,
                                                    *config_manager->get<float>("/algorithm_settings/cluster/eps"),
                                                    *config_manager->get<int>("/algorithm_settings/cluster/min_pts"),
                                                    *config_manager->get<int>("/algorithm_settings/cluster/num_threads"));
                                                }

        // 更新跟踪器
        std::vector<TrackResult> tracks = tracker->update(clustered);
        spdlog::debug("跟踪结果数量: {}", tracks.size());

        // 获取所有活跃跟踪器的预测结果
        std::vector<TrackResult> all_active_tracks = tracker->getAllActiveTracks(120);

        // 转换跟踪结果
        if (all_active_tracks.empty()) {
            *num_tracks = 0;
            ReleaseInternalDetectionData(detection_data);
            MemoryMonitor::logMemoryUsage("目标跟踪完成");
            MemoryMonitor::logCPUMemoryUsage("目标跟踪完成");
            return;
        }

        // 限制输出结果数量
        int actual_tracks = std::min(static_cast<int>(all_active_tracks.size()), 256);
        *num_tracks = actual_tracks;

        if (actual_tracks < static_cast<int>(tracks.size())) {
            spdlog::warn("跟踪结果数量 {} 超过最大限制 {}，只输出前 {} 个结果",
                        tracks.size(), 256, actual_tracks);
        }

        // 填充跟踪结果
        for (int i = 0; i < actual_tracks; ++i) {
            const auto& track = all_active_tracks[i];

            tracking_results[i].id = track.id;
            tracking_results[i].x = track.position[0];
            tracking_results[i].y = track.position[1];
            tracking_results[i].z = track.position[2];
            tracking_results[i].vx = track.velocity[0];
            tracking_results[i].vy = track.velocity[1];
            tracking_results[i].vz = track.velocity[2];

            // 计算转换值
            float range = std::sqrt(track.position[0]*track.position[0] +
                                    track.position[1]*track.position[1] +
                                    track.position[2]*track.position[2]);
            tracking_results[i].fMR = range;
            tracking_results[i].fMV = (track.position[0]*track.velocity[0] +
                                            track.position[1]*track.velocity[1] +
                                            track.position[2]*track.velocity[2]) / (range + 1e-6f);
            tracking_results[i].fMA = std::fmod(std::atan2(track.position[1], track.position[0]) * 180.0f / M_PI + 360.0f, 360.0f);
            tracking_results[i].fME = std::atan2(track.position[2], std::sqrt(track.position[0]*track.position[0] + track.position[1]*track.position[1])) * 180.0f / M_PI;


            // 设置默认值
            tracking_results[i].fSNR = 1.0f;
            tracking_results[i].fEn = 1.0f;
            tracking_results[i].fRcs = 1.0f;
            tracking_results[i].type = 1;
            tracking_results[i].FPGATimeLog = 1;
            tracking_results[i].PreShow = 2;

            spdlog::info("Track[{:>2}] ID: {:>2} | Pos: ({:>7.2f}, {:>7.2f}, {:>7.2f}) m | Vel: ({:>6.2f}, {:>6.2f}, {:>6.2f}) m/s | R: {:>6.2f} m | Vr: {:>6.2f} m/s | Az: {:>6.2f}° | El: {:>6.2f}°",
                i,
                tracking_results[i].id,
                tracking_results[i].x, tracking_results[i].y, tracking_results[i].z,
                tracking_results[i].vx, tracking_results[i].vy, tracking_results[i].vz,
                tracking_results[i].fMR, tracking_results[i].fMV,
                tracking_results[i].fMA, tracking_results[i].fME);
        }

        spdlog::debug("跟踪完成，输出 {} 个轨迹", *num_tracks);

        ReleaseInternalDetectionData(detection_data);
        MemoryMonitor::logMemoryUsage("目标跟踪完成");
        MemoryMonitor::logCPUMemoryUsage("目标跟踪完成");

        return;

    } catch (const std::exception& e) {
        spdlog::error("Exception in TargetTracking: {}", e.what());
        ReleaseInternalDetectionData(detection_data);
        *num_tracks = 0;
        return;
    }
}

// 释放内部检测数据内存
ALGORITHM_API void ReleaseInternalDetectionData(InternalDetectionData* detection_data) {

    if (!detection_data) {
        spdlog::debug("无检测目标数据");
        return;
    }

    spdlog::debug("开始释放检测结果数组");
    // 释放检测结果数组
    if (detection_data->detection_results) {
        delete[] detection_data->detection_results;
        detection_data->detection_results = nullptr;
    }
    detection_data->num_detections = 0;

    spdlog::debug("结束释放检测结果数组");

    spdlog::debug("开始释放列数据段");
    // 释放列数据段及其内部数组 - 增加边界检查
    if (detection_data->column_segments) {
        if (detection_data->num_segments > 0) {
            const int MAX_SAFE_SEGMENTS = 10000;
            int safe_segments = std::min(detection_data->num_segments, MAX_SAFE_SEGMENTS);
            for (int i = 0; i < safe_segments; ++i) {
                // ensure pointers are not garbage
                if (detection_data->column_segments[i].data_S) {
                    delete[] detection_data->column_segments[i].data_S;
                    detection_data->column_segments[i].data_S = nullptr;
                }
                if (detection_data->column_segments[i].data_D) {
                    delete[] detection_data->column_segments[i].data_D;
                    detection_data->column_segments[i].data_D = nullptr;
                }
            }
        }
        delete[] detection_data->column_segments;
        detection_data->column_segments = nullptr;
        detection_data->num_segments = 0;
    }
    spdlog::debug("结束释放列数据段");

    // 释放复制的帧头数据
    if (detection_data->S_head) {
        delete[] detection_data->S_head;
        detection_data->S_head = nullptr;
    }

    spdlog::debug("已释放内部检测数据内存");
}

// 释放所有资源
ALGORITHM_API void ReleaseAllResources() {
    try {
        spdlog::info("Resource cleanup started...");

        // 1. 清理 OpenCV 线程池和全局状态
        try {
            spdlog::info("Cleaning up OpenCV resources...");

            // 强制执行一个简单的并行操作来触发线程池清理
            cv::parallel_for_(cv::Range(0, 1), [](const cv::Range&){});

            // 清理所有 OpenCV 窗口
            cv::destroyAllWindows();

            spdlog::info("OpenCV resources cleaned up");
        } catch (const std::exception& e) {
            spdlog::warn("Exception during OpenCV cleanup: {}", e.what());
        }

        // 2. 清理统一资源管理器
        scr5000::cleanupResourceManager();

        // 3. 重置库初始化状态
        s_lib_initialized.store(false, std::memory_order_release);
        s_init_failed.store(false, std::memory_order_release);

        // 4. 释放全局资源管理器（如果还有的话）
        releaseGPUResourceManager();

        // 5. 同步所有 CUDA 流并重置设备
        try {
            spdlog::info("Synchronizing CUDA streams and resetting device...");

            // 同步所有 CUDA 流
            cudaError_t sync_status = cudaDeviceSynchronize();
            if (sync_status != cudaSuccess) {
                spdlog::warn("CUDA device synchronization failed: {}", cudaGetErrorString(sync_status));
            }
            cv::setNumThreads(0);
            cv::parallel_for_(cv::Range(0, 1), [](const cv::Range&){});
            cv::destroyAllWindows();
            // 重置CUDA设备以确保所有资源被释放
            cudaError_t reset_status = cudaDeviceReset();
            if (reset_status != cudaSuccess) {
                spdlog::warn("CUDA device reset failed: {}", cudaGetErrorString(reset_status));
            } else {
                spdlog::info("CUDA device reset successfully");
            }
        } catch (const std::exception& e) {
            spdlog::warn("Exception during CUDA cleanup: {}", e.what());
        }

        spdlog::info("All resources released successfully");

    } catch (const std::exception& e) {
        spdlog::error("Exception during resource cleanup: {}", e.what());
    }
}